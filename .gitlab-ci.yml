db_backup:
  variables:
    BACKUP_DB_SCRIPT_PATH: /root/mpg-droplet/mpg-services/backup-db.sh
    SWARM_DB_SERVICE: mpg-prod_postgres-db
    DATABASES: training nutrition grafana keycloak notifications payments
  only:
    - schedules
  image: alpine
  stage: deploy
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" >> ~/.ssh/id_dsa
    - chmod 600 ~/.ssh/id_dsa
    - echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config
  script:
    - ssh <EMAIL> "chmod +x $BACKUP_DB_SCRIPT_PATH && $BACKUP_DB_SCRIPT_PATH $SWARM_DB_SERVICE $DATABASES"

db_checkpoint_failure_recovery:
  stage: deploy
  variables:
    DOCKER_IMAGE: postgres:13
    DATA_VOLUME: mpg-prod_mpg-postgres-prod-db-data
    CONTAINER_COMMAND: /usr/lib/postgresql/13/bin/pg_resetwal /var/lib/postgresql/data
  image: alpine
  rules:
    - when: manual
  script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" >> ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config
    - ssh <EMAIL> "docker run --rm -v $DATA_VOLUME:/var/lib/postgresql/data $DOCKER_IMAGE bash -c 'su - postgres -c \"$CONTAINER_COMMAND\"'"
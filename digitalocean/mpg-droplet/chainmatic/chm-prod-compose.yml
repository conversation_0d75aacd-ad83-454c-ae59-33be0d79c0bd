version: '3.8'

services:
  proposals:
    image: registry.gitlab.com/chainmatic/proposals:prod
    deploy:
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik-public
        - traefik.constraint-label=traefik-public
        - traefik.http.middlewares.https-redirect.redirectscheme.scheme=https
        - traefik.http.middlewares.https-redirect.redirectscheme.permanent=true
        - traefik.http.routers.chm-proposals-http.rule=Host(`chainmatic.ai`)
        - traefik.http.routers.chm-proposals-http.entrypoints=http
        - traefik.http.routers.chm-proposals-http.middlewares=https-redirect
        - traefik.http.routers.chm-proposals-https.rule=Host(`chainmatic.ai`)
        - traefik.http.routers.chm-proposals-https.entrypoints=https
        - traefik.http.routers.chm-proposals-https.tls=true
        - traefik.http.routers.chm-proposals-https.tls.certresolver=le
        - traefik.http.services.chm-proposals-service.loadbalancer.server.port=4201
    ports:
      - "4201:4201"
    environment:
      PORT: 4201
      SUPABASE_URL: ${SUPABASE_URL}
      SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY}
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      BASE_URL: https://chainmatic.ai
    networks:
      - traefik-public
      - chm
networks:
  traefik-public:
    external: true
  chm:
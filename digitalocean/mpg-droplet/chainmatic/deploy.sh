#!/bin/bash

if [ -z "$1" ]
  then
    echo "Please provide environment name (e.g. dev, prod)"
    exit 1
fi

DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ENV="$1"

get-ssm-param(){
    SSM_PARAM=$(aws ssm get-parameter --profile "chainmatic" --region eu-west-2 --name "$1" --with-decryption --output text --query Parameter.Value)
    echo "$SSM_PARAM"
}

export SUPABASE_URL=$(get-ssm-param "/chm-$ENV/supabase/url")
export SUPABASE_ANON_KEY=$(get-ssm-param "/chm-$ENV/supabase/anon-key")
export STRIPE_SECRET_KEY=$(get-ssm-param "/chm-$ENV/stripe/secret-key")
export STRIPE_WEBHOOK_SECRET=$(get-ssm-param "/chm-$ENV/stripe/webhook-secret")
export AWS_ACCESS_KEY_ID=$(get-ssm-param "/chm-$ENV/aws/access-key-id")
export AWS_SECRET_ACCESS_KEY=$(get-ssm-param "/chm-$ENV/aws/secret-access-key")
export INVBG_API_KEY=$(get-ssm-param "/chm-$ENV/invbg/api-key")
export JWT_SECRET=$(get-ssm-param "/chm-$ENV/jwt/secret")
export OPENAI_API_KEY=$(get-ssm-param "/chm-$ENV/openai/api-key")

docker stack deploy -c "${DIR}/chm-$ENV-compose.yml" "chm-$ENV" --with-registry-auth

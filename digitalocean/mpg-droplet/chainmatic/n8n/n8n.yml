version: '3.8'

services:
  n8n:
    image: n8nio/n8n:latest
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=${N8N_BASIC_AUTH_USER}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_BASIC_AUTH_PASSWORD}
      - N8N_HOST=n8n.chainmatic.ai
      - N8N_PORT=5678
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_HOST=${MPG_PROD_DB_ADDRESS}
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_USER=${MPG_PROD_DB_USERNAME}
      - DB_POSTGRESDB_PASSWORD=${MPG_PROD_DB_PASSWORD}
      - DB_POSTGRESDB_SCHEMA=public
      - EXECUTIONS_PROCESS=main
      - WEBHOOK_URL=https://n8n.chainmatic.ai
    networks:
      - traefik-public
    volumes:
      - n8n_data:/home/<USER>/.n8n
    ports:
      - "5678:5678"
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik-public
        - traefik.constraint-label=traefik-public
        - traefik.http.middlewares.https-redirect.redirectscheme.scheme=https
        - traefik.http.middlewares.https-redirect.redirectscheme.permanent=true
        - traefik.http.routers.n8n-http.rule=Host(`n8n.chainmatic.ai`)
        - traefik.http.routers.n8n-http.entrypoints=http
        - traefik.http.routers.n8n-http.middlewares=https-redirect
        - traefik.http.routers.n8n-https.rule=Host(`n8n.chainmatic.ai`)
        - traefik.http.routers.n8n-https.entrypoints=https
        - traefik.http.routers.n8n-https.tls=true
        - traefik.http.routers.n8n-https.tls.certresolver=le
        - traefik.http.services.n8n.loadbalancer.server.port=5678
networks:
  traefik-public:
    external: true

volumes:
  n8n_data:

#!/bin/bash

set -e

STACK_NAME="n8n"
SERVICE_NAME="${STACK_NAME}_n8n"
IMAGE_NAME="n8nio/n8n:latest"

echo "Pulling the latest image for $IMAGE_NAME..."
docker pull $IMAGE_NAME

echo "Updating the $SERVICE_NAME service in the $STACK_NAME stack..."
docker service update \
  --image $IMAGE_NAME \
  --detach=true \
  --force \
  $SERVICE_NAME

echo "Update triggered. Use 'docker service ps $SERVICE_NAME' to monitor progress."
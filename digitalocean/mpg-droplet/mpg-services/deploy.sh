#!/bin/bash

if [ -z "$1" ]
  then
    echo "Please provide environment name (e.g. dev, prod)"
    exit 1
fi

DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ENV="$1"

get-ssm-param(){
    SSM_PARAM=$(aws ssm get-parameter --profile "mpg-$ENV" --region eu-west-2 --name "$1" --with-decryption --output text --query Parameter.Value)
    echo "$SSM_PARAM"
}

export MPG_KEYCLOAK_USERNAME=$(get-ssm-param "/mpg-$ENV/keycloak/username")
export MPG_KEYCLOAK_PASSWORD=$(get-ssm-param "/mpg-$ENV/keycloak/password")

export MPG_DB_USERNAME=$(get-ssm-param "/mpg-$ENV/rds/username")
export MPG_DB_PASSWORD=$(get-ssm-param "/mpg-$ENV/rds/password")
export MPG_DB_ADDRESS=$(get-ssm-param "/mpg-$ENV/rds/address")

export MPG_TRAINING_SERVICE_KEYCLOAK_SECRET=$(get-ssm-param "/mpg-$ENV/training-service/keycloak-secret")
export MPG_NUTRITION_SERVICE_KEYCLOAK_SECRET=$(get-ssm-param "/mpg-$ENV/nutrition-service/keycloak-secret")
export MPG_NOTIFICATIONS_SERVICE_KEYCLOAK_SECRET=$(get-ssm-param "/mpg-$ENV/notifications-service/keycloak-secret")
export MPG_RTC_SERVICE_KEYCLOAK_SECRET=$(get-ssm-param "/mpg-$ENV/rtc-service/keycloak-secret")
export MPG_PAYMENTS_KEYCLOAK_SECRET=$(get-ssm-param "/mpg-$ENV/payments-service/keycloak-secret")

export REDIS_HOST=$(get-ssm-param "/mpg-$ENV/redis/host")
export REDIS_PASSWORD=$(get-ssm-param "/mpg-$ENV/redis/password")

export MPG_AWS_ACCESS_KEY_ID=$(get-ssm-param "/mpg-$ENV/mpg-service/aws/access-key-id")
export MPG_SECRET_ACCESS_KEY=$(get-ssm-param "/mpg-$ENV/mpg-service/aws/secret-access-key")

export OPENAI_API_KEY=$(get-ssm-param "/mpg-$ENV/open-ai/api-key")
export FATSECRET_CLIENT_ID=$(get-ssm-param "/mpg-$ENV/fat-secret/client-id")
export FATSECRET_CLIENT_SECRET=$(get-ssm-param "/mpg-$ENV/fat-secret/client-secret")

export MYPOS_SID=$(get-ssm-param "/mpg-$ENV/mypos/sid")
export MYPOS_WALLET_NUMBER=$(get-ssm-param "/mpg-$ENV/mypos/wallet-number")
export MYPOS_KEY_INDEX=$(get-ssm-param "/mpg-$ENV/mypos/key-index")
export MYPOS_PUBLIC_KEY=$(get-ssm-param "/mpg-$ENV/mypos/public-key")
export MYPOS_PRIVATE_KEY=$(get-ssm-param "/mpg-$ENV/mypos/private-key")
export MYPOS_CLIENT_ID=$(get-ssm-param "/mpg-$ENV/mypos/client-id")
export MYPOS_CLIENT_SECRET=$(get-ssm-param "/mpg-$ENV/mypos/client-secret")

export WHOOP_CLIENT_ID=$(get-ssm-param "/mpg-$ENV/whoop/client-id")
export WHOOP_CLIENT_SECRET=$(get-ssm-param "/mpg-$ENV/whoop/client-secret")

export DISCORD_BOT_TOKEN=$(get-ssm-param "/mpg-$ENV/discord/bot-token")
export DISCORD_CLIENT_ID=$(get-ssm-param "/mpg-$ENV/discord/client-id")
export DISCORD_CLIENT_SECRET=$(get-ssm-param "/mpg-$ENV/discord/client-secret")

export INVITATIONS_API_KEY=$(get-ssm-param "/mpg-$ENV/invitations/api-key")
export MANY_CHAT_API_KEY=$(get-ssm-param "/mpg-$ENV/many-chat/api-key")

docker stack deploy -c "${DIR}/mpg-$ENV-compose.yml" "mpg-$ENV" --with-registry-auth

#!/bin/bash

DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

get-ssm-param(){
    SSM_PARAM=$(aws ssm get-parameter --profile mpg-prod --region eu-west-2 --name "$1" --with-decryption --output text --query Parameter.Value)
    echo "$SSM_PARAM"
}

export MPG_PROD_PGADMIN_EMAIL=$(get-ssm-param "/mpg-prod/pgadmin/email")
export MPG_PROD_PGADMIN_PASSWORD=$(get-ssm-param "/mpg-prod/pgadmin/password")

docker stack deploy -c "${DIR}/pgadmin.yml" pgadmin
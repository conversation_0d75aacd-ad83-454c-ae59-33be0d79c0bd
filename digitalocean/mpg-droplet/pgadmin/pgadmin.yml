version: '3'

services:
  pgadmin:
    image: dpage/pgadmin4:8.3
    deploy:
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik-public
        - traefik.constraint-label=traefik-public
        - traefik.http.middlewares.https-redirect.redirectscheme.scheme=https
        - traefik.http.middlewares.https-redirect.redirectscheme.permanent=true
        - traefik.http.routers.pgadmin-http.rule=Host(`pgadmin.myprogressguru.com`)
        - traefik.http.routers.pgadmin-http.entrypoints=http
        - traefik.http.routers.pgadmin-http.middlewares=https-redirect
        - traefik.http.routers.pgadmin-https.rule=Host(`pgadmin.myprogressguru.com`)
        - traefik.http.routers.pgadmin-https.entrypoints=https
        - traefik.http.routers.pgadmin-https.tls=true
        - traefik.http.routers.pgadmin-https.tls.certresolver=le
        - traefik.http.routers.pgadmin-https.middlewares=admin-auth
        - traefik.http.services.pgadmin-service.loadbalancer.server.port=5050
    environment:
      PGADMIN_DEFAULT_EMAIL: ${MPG_PROD_PGADMIN_EMAIL}
      PGADMIN_DEFAULT_PASSWORD: ${MPG_PROD_PGADMIN_PASSWORD}
      PGADMIN_LISTEN_PORT: 5050
    ports:
      - "5050:5050"
    volumes:
    - pgadmin-data:/var/lib/pgadmin
    networks:
      - traefik-public

networks:
  traefik-public:
    external: true

volumes:
  pgadmin-data:
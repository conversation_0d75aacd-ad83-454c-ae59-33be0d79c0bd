# Global tags can be specified here in key="value" format.
[global_tags]

# Configuration for telegraf agent
[agent]
  interval = "10s"
  round_interval = true
  metric_batch_size = 1000
  metric_buffer_limit = 10000
  collection_jitter = "0s"
  flush_interval = "10s"
  flush_jitter = "0s"
  precision = ""
  hostname = ""
  omit_hostname = false

# Outputs section (InfluxDB)
[[outputs.influxdb_v2]]
  urls = ["http://influxdb:8086"]  # InfluxDB URL
  bucket = "telegraf"
  token = "Tm3L9JCIBhAoBEu92S0nGBjTMcQbQTBy-qdRzA4pPw-11Vnf9fZGF2Hsy838D3McK1fGTUsqNXTFHsL3B3nQPw=="
  organization = "myprogressguru"

# Input Plugins
[[inputs.docker]]
  endpoint = "unix:///var/run/docker.sock"
  gather_services = true
  container_names = []
  container_name_include = []
  container_name_exclude = []
  timeout = "5s"
  perdevice = true
  total = false
  docker_label_include = []
  docker_label_exclude = []

[[inputs.docker_log]]
  endpoint = "unix:///var/run/docker.sock"

#!/bin/bash

#Server setup
ufw allow OpenSSH
ufw enable


#Docker setup
sudo apt update
sudo apt install apt-transport-https ca-certificates curl software-properties-common
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -
sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu focal stable"
apt-cache policy docker-ce
sudo apt install docker-ce
sudo usermod -aG docker ${USER}
su - ${USER}

#Monitoring agent
curl -sSL https://repos.insights.digitalocean.com/install.sh | sudo bash
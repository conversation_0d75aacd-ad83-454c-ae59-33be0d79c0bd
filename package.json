{"name": "mobile-client", "version": "0.0.1", "author": "com.myprogressguru", "homepage": "https://myprogressguru.com/", "scripts": {"ng": "ng", "ionic": "ionic", "start": "ionic serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^17.3.12", "@angular/cdk": "^16.2.0", "@angular/common": "^17.3.12", "@angular/core": "^17.3.12", "@angular/fire": "^7.6.1", "@angular/forms": "^17.3.12", "@angular/platform-browser": "^17.3.12", "@angular/platform-browser-dynamic": "^17.3.12", "@angular/pwa": "^16.2.0", "@angular/router": "^17.3.12", "@angular/service-worker": "^17.3.12", "@auth0/angular-jwt": "^5.1.2", "@eisberg-labs/ngx-barcode-scanner": "^6.0.0", "@ionic/angular": "^8.2.6", "@ionic/pwa-elements": "^3.2.2", "@ionic/storage-angular": "^4.0.0", "@messageformat/core": "^3.2.0", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@swimlane/ngx-charts": "^20.4.1", "@types/papaparse": "^5.3.14", "apexcharts": "^3.44.0", "canvas-confetti": "^1.9.2", "firebase": "10.2.0", "firebase-tools": "^12.4.8", "howler": "^2.2.4", "html2canvas": "^1.4.1", "keycloak-angular": "^14.0.0", "keycloak-js": "21.0.1", "lodash": "^4.17.21", "lottie-web": "^5.13.0", "luxon": "^3.4.0", "mypos-embedded-checkout": "^1.4.4", "ng-apexcharts": "^1.8.0", "ng2-charts": "^5.0.2", "ngx-lottie": "^20.0.0", "ngx-panzoom": "^16.0.0", "ngx-translate-messageformat-compiler": "^6.5.0", "papaparse": "^5.4.1", "plyr": "^3.7.8", "rxjs": "~7.8.1", "socket.io-client": "^4.7.2", "swiper": "^11.0.1", "tslib": "^2.6.1", "zone.js": "~0.14.8"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.8", "@angular/cli": "^17.3.8", "@angular/compiler": "^17.3.12", "@angular/compiler-cli": "^17.3.12", "@angular/language-service": "^17.3.12", "@capacitor/cli": "5.2.3", "@ionic/angular-toolkit": "^10.0.0", "@types/canvas-confetti": "^1.6.4", "@types/howler": "^2.2.9", "@types/jasmine": "~4.3.5", "@types/jasminewd2": "~2.0.10", "@types/lodash": "^4.14.197", "@types/luxon": "^3.3.1", "@types/node": "^20.5.0", "codelyzer": "^6.0.2", "jasmine-core": "~5.1.0", "jasmine-spec-reporter": "~7.0.0", "karma": "~6.4.2", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.1", "karma-coverage-istanbul-reporter": "~3.0.3", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "prettier": "3.0.1", "protractor": "~7.0.0", "ts-node": "~10.9.1", "tslint": "~6.1.0", "typescript": "~5.4.5"}, "description": "My Progress Guru"}
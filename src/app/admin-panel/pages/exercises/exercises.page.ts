import { Component } from '@angular/core';
import { Exercise, TrainingVolume } from '../../../training/models';
import { ExerciseService } from '../../../training/services';
import { ViewDidEnter } from '@ionic/angular';
import { AlertService, ModalService } from '../../../shared/services';
import { CreateExerciseModalComponent } from '../../../training/components/create-exercise-modal/create-exercise-modal.component';
import { Button } from '../../../shared/models';

@Component({
  selector: 'mpg-exercises',
  templateUrl: './exercises.page.html',
  styleUrls: ['./exercises.page.scss'],
})
export class ExercisesPage implements ViewDidEnter {
  exercises: Exercise[] = [];
  filteredExercises: Exercise[] = [];

  constructor(
    private exerciseService: ExerciseService,
    private modalService: ModalService,
    private alertService: AlertService,
  ) {}

  ionViewDidEnter() {
    this.fetchExercises();
  }

  handleSearch(event: any) {
    const search = event.detail.value;
    if (search === '') {
      this.filteredExercises = this.exercises.slice(0);
    }

    this.filteredExercises = this.exercises.filter(
      (e) =>
        e.name.toLowerCase().includes(search.toLowerCase()) ||
        e.nameBg?.toLowerCase()?.includes(search.toLowerCase()),
    );
  }

  handleCreateExercise({
    exercise,
    trainingVolume,
  }: {
    exercise?: Exercise;
    trainingVolume?: TrainingVolume;
  } = {}) {
    this.modalService
      .create(
        {
          component: CreateExerciseModalComponent,
          componentProps: {
            exercise,
            trainingVolume,
          },
        },
        'small',
      )
      .subscribe(() => this.fetchExercises());
  }

  getMajorMuscleGroupKeys(exercise: Exercise): string[] {
    return this.exerciseService.getMajorMuscleGroupKeys(
      exercise.trainingVolume,
    );
  }

  getAssistingMuscleGroupKeys(exercise: Exercise): string[] {
    return this.exerciseService.getAssistingMuscleGroupKeys(
      exercise.trainingVolume,
    );
  }

  getPopoverMenuButtons(exercise: Exercise): Button[] {
    return [
      {
        label: 'buttons.edit',
        handler: () => {
          this.handleCreateExercise({ exercise });
        },
      },
      {
        label: 'buttons.delete',
        handler: () => {
          this.alertService.createDeleteAlert(() => {
            this.exerciseService
              .delete(exercise.id)
              .subscribe(() => this.fetchExercises());
          });
        },
      },
      {
        label: 'training.copy-volume',
        handler: () => {
          this.handleCreateExercise({
            trainingVolume: exercise.trainingVolume,
          });
        },
      },
    ];
  }

  private fetchExercises() {
    this.exerciseService.getAll().subscribe((exercises) => {
      this.exercises = exercises;
      this.filteredExercises = exercises;
    });
  }
}

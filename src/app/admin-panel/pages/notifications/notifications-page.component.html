<ion-header>
  <ion-toolbar color="dark">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>Notifications</ion-title>
    <ion-buttons slot="primary">
      <ion-button (click)="handlePopoverMenu($event)">
        <ion-icon
          ios="ellipsis-horizontal"
          md="ellipsis-vertical"
          slot="icon-only"
        ></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
  <ion-toolbar color="dark">
    <ion-row>
      <ion-col
        class="ion-no-padding"
        offset-lg="3"
        offset-xl="4"
        size-lg="6"
        size-xl="4"
      >
        <ion-searchbar
          (ionChange)="handleSearch($event)"
          [debounce]="100"
          [placeholder]="'search' | translate"
        ></ion-searchbar>
      </ion-col>
    </ion-row>
  </ion-toolbar>
</ion-header>

<ion-fab #fab class="mb" horizontal="end" slot="fixed" vertical="bottom">
  <ion-fab-button>
    <ion-icon name="add"></ion-icon>
  </ion-fab-button>
  <ion-fab-list side="top">
    <ion-fab-button (click)="handleCreateSound()" data-desc="Create Sound">
      <ion-icon name="musical-notes"></ion-icon>
    </ion-fab-button>
  </ion-fab-list>
</ion-fab>
<ion-content (click)="fab.close()">
  <ion-row>
    <ion-col
      *ngFor="let sound of filteredSounds"
      offset-lg="3"
      size="12"
      size-lg="6"
    >
      <mpg-card
        [popoverMenuButtons]="getPopoverMenuButtons(sound)"
        [title]="sound.name"
      >
        <ion-row class="border-bottom">
          <ion-col class="flex-centered" size="12">
            <audio controls>
              <source [src]="sound.url" />
            </audio>
          </ion-col>
        </ion-row>
      </mpg-card>
    </ion-col>
  </ion-row>
</ion-content>

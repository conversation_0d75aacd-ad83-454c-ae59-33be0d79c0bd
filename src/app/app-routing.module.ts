import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';
import { appInitializedGuard, AuthGuard } from './core/guards';

const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: 'trainee',
  },
  {
    path: 'legal',
    loadChildren: () =>
      import('./trainee-panel/pages/legal/legal.module').then(
        (m) => m.LegalPageModule,
      ),
    data: {
      showMenuButton: false,
    },
  },
  {
    path: 'trainee',
    loadChildren: () =>
      import('./trainee-panel/trainee-panel.module').then(
        (m) => m.TraineePanelModule,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: ['trainee'],
    },
  },
  {
    path: 'trainer',
    loadChildren: () =>
      import('./trainer-panel/trainer-panel.module').then(
        (m) => m.TrainerPanelModule,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: ['trainer'],
    },
  },
  {
    path: 'admin',
    loadChildren: () =>
      import('./admin-panel/admin-panel.module').then(
        (m) => m.AdminPanelModule,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: ['admin'],
    },
  },
  {
    path: 'initial-setup',
    loadChildren: () =>
      import('./auth/pages/initial-setup/initial-setup.module').then(
        (m) => m.InitialSetupPageModule,
      ),
    canActivate: [appInitializedGuard],
  },
  {
    path: 'whoop-callback',
    loadChildren: () =>
      import('./auth/pages/whoop-callback/whoop-callback.module').then(
        (m) => m.WhoopCallbackPageModule,
      ),
    canActivate: [AuthGuard],
  },
  {
    path: 'discord-callback',
    loadChildren: () =>
      import('./auth/pages/discord-callback/discord-callback.module').then(
        (m) => m.DiscordCallbackPageModule,
      ),
    canActivate: [AuthGuard],
  },
  {
    path: 'invitation/:id',
    loadChildren: () =>
      import('./auth/pages/invitation/invitation.module').then(
        (m) => m.InvitationPageModule,
      ),
    canActivate: [],
  },
  {
    path: 'introduction',
    loadChildren: () =>
      import('./auth/pages/introduction/introduction.module').then(
        (m) => m.IntroductionPageModule,
      ),
  },
  {
    path: 'lottie-demo',
    loadChildren: () =>
      import('./lottie-demo/lottie-demo.module').then(
        (m) => m.LottieDemoPageModule,
      ),
  },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}

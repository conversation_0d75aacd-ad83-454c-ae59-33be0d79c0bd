import { APP_INITIALIZER, NgModule } from '@angular/core';
import { RouteReuseStrategy } from '@angular/router';

import { IonicModule, IonicRouteStrategy } from '@ionic/angular';

import { AppComponent } from './app.component';
import { AppRoutingModule } from './app-routing.module';
import {
  HTTP_INTERCEPTORS,
  HttpClient,
  HttpClientModule,
} from '@angular/common/http';

import { SideMenuComponentModule } from './shared/components/side-menu/side-menu.module';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { BrowserModule } from '@angular/platform-browser';

import { IonicStorageModule } from '@ionic/storage-angular';
import { KeycloakAngularModule, KeycloakService } from 'keycloak-angular';
import { environment } from '../environments/environment';
import { TraineeService } from './training/services';
import { DatePipe } from '@angular/common';
import { LoadingSpinnerComponentModule } from './shared/components/loading-spinner/loading-spinner.module';
import {
  ApplicationLocalizationInitializationFactory,
  ApplicationLocalizationInitializationService,
} from './core/initialization/localization';
import {
  MissingTranslationHandler,
  TranslateCompiler,
  TranslateLoader,
  TranslateModule,
} from '@ngx-translate/core';
import { TranslateMessageFormatCompiler } from 'ngx-translate-messageformat-compiler';
import { TrainingModalsModule } from './training/modules/training-modals.module';
import { EntitySelectorModalModule } from './shared/components/entity-selector-modal/entity-selector-modal.module';
import { SharedModalsModule } from './shared/modules/shared-modals.module';
import { ServiceWorkerModule } from '@angular/service-worker';
import { AngularFireModule } from '@angular/fire/compat';
import {
  AngularFireMessagingModule,
  VAPID_KEY,
} from '@angular/fire/compat/messaging';
import { NotificationsModalsModule } from './notifications/modules/notifications-modals.module';
import { NoAuthInterceptor } from './core/interceptors';
import { MPGMissingTranslationHandler } from './core/initialization/localization/mpg-missing-translation.handler';
import { NotificationsFabModule } from './notifications/components/notifications-fab/notifications-fab.module';
import { register } from 'swiper/element/bundle';
import { NgxPanZoomModule } from 'ngx-panzoom';
import { RedirectBackFabModule } from './shared/components/redirect-back-fab/redirect-back-fab.module';
import { NutritionModalsModule } from './nutrition/modules/nutrition-modals.module';
import { NgxBarcodeScannerModule } from '@eisberg-labs/ngx-barcode-scanner';
import { PaymentsModalsModule } from './payments/modules/payments-modals.module';
import { AuthModalsModule } from './auth/modules/auth-modals.module';

function initializeKeycloak(keycloak: KeycloakService) {
  return () =>
    keycloak.init({
      config: {
        url: environment.KEYCLOAK_URL,
        realm: 'myprogressguru',
        clientId: 'frontend',
      },
      initOptions: {
        onLoad: 'check-sso',
        silentCheckSsoRedirectUri:
          window.location.origin + '/assets/silent-check-sso.html',
      },
      bearerExcludedUrls: [`${TraineeService.BASE_URL}/any`],
    });
}

@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    IonicModule.forRoot({
      mode: 'md',
    }),
    IonicStorageModule.forRoot(),
    AppRoutingModule,
    HttpClientModule,
    SideMenuComponentModule,
    KeycloakAngularModule,
    LoadingSpinnerComponentModule,
    AuthModalsModule,
    SharedModalsModule,
    TrainingModalsModule,
    NotificationsModalsModule,
    NutritionModalsModule,
    PaymentsModalsModule,
    EntitySelectorModalModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory:
          ApplicationLocalizationInitializationFactory.createTranslateLoader,
        deps: [HttpClient],
      },
      compiler: {
        provide: TranslateCompiler,
        useClass: TranslateMessageFormatCompiler,
      },
      missingTranslationHandler: {
        provide: MissingTranslationHandler,
        useClass: MPGMissingTranslationHandler,
      },
    }),
    ServiceWorkerModule.register('combined-sw.js', {
      enabled: environment.production,
      // Register the ServiceWorker as soon as the application is stable
      // or after 30 seconds (whichever comes first).
      registrationStrategy: 'registerWhenStable:30000',
    }),
    AngularFireModule.initializeApp(environment.FIREBASE),
    AngularFireMessagingModule,
    NotificationsFabModule,
    NgxPanZoomModule,
    RedirectBackFabModule,
    NgxBarcodeScannerModule,
    LottieModule.forRoot({ player: () => player }),
  ],
  providers: [
    {
      provide: APP_INITIALIZER,
      useFactory: initializeKeycloak,
      multi: true,
      deps: [KeycloakService],
    },
    {
      provide: RouteReuseStrategy,
      useClass: IonicRouteStrategy,
    },
    {
      provide: APP_INITIALIZER,
      useFactory:
        ApplicationLocalizationInitializationFactory.getApplicationLocalizationInitializationFunction,
      deps: [ApplicationLocalizationInitializationService],
      multi: true,
    },
    { provide: VAPID_KEY, useValue: environment.FCM_PUBLIC_KEY },
    DatePipe,
    { provide: HTTP_INTERCEPTORS, useClass: NoAuthInterceptor, multi: true },
  ],
  bootstrap: [AppComponent],
})
export class AppModule {
  constructor() {
    register();
  }
}

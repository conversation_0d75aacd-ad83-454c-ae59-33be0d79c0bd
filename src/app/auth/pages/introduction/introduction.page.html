<ion-content [fullscreen]="true">
  <ion-row>
    <ion-col offset-lg="2" size="12" size-lg="8">
      <div class="header">
        <img src="assets/icon/favicon.png" />
        <h1>My Progress Guru</h1>
        <h2>
          <strong class="primary">Единствената</strong> апликация за фитнес, от
          която се нуждаеш
        </h2>
        <h3>
          Стига вече въвеждане на <strong class="primary">хранене</strong> в
          една, <strong class="primary">тренировки</strong> в друга.
        </h3>
      </div>

      <video
        (onPlayerReady)="player = $event"
        mpgVideoPlayer
        src="https://youtu.be/TsqZPdZBGzQ"
      ></video>

      <div
        (click)="handleStartFree()"
        class="cta ion-activatable overflow-hidden mt"
      >
        <ion-ripple-effect></ion-ripple-effect>
        Започни безплатно
      </div>

      <section>
        <h2>
          <strong
            >„Не можеш да <strong class="primary">подобриш</strong> нещо, което
            не <strong class="primary">измерваш</strong>.“</strong
          >
        </h2>
        <p>
          Случвало ли ти се е да ходиш до фитнес залата
          <strong class="primary">без да помниш</strong> с колко кила си правил
          миналия път? Най-честото решение, което съм забелязал хората да
          ползват е да си пишат тренировките в
          <strong class="primary">тефтер</strong>, в
          <strong class="primary">notes</strong> на телефона или да се мъчат с
          <strong class="primary">Excel</strong> файлове. Това е много неудобно
          и неефективно, тъй като трудно биха могли да се направят анализи и да
          се визуализира прогреса чрез графики.
        </p>
      </section>
      <section>
        <div class="images">
          <img src="assets/images/introduction-1.jpg" />
          <img src="assets/images/introduction-2.png" />
          <img src="assets/images/introduction-3.png" />
        </div>
      </section>
      <section>
        <h2>
          <strong
            >Следи, анализирай,
            <strong class="primary">прогресирай</strong></strong
          >
        </h2>
        <p>
          Това са трите основни стъпки във философията на
          <strong class="primary">My Progress Guru</strong>. С воденето на
          отчетност на всичко свързано с фитнеса, ти ще можеш да откриеш какво
          работи за теб и какво не. Ще можеш да се научиш да правиш
          <strong class="primary">по-добри</strong> тренировки, да се храниш
          <strong class="primary">по-здравословно</strong> и да се чувстваш
          <strong class="primary">по-добре</strong> в своето тяло.
        </p>
      </section>

      <section>
        <h1>Какво включва апликацията?</h1>
        <p *ngFor="let feature of features">
          <ion-label class="ion-text-wrap ion-text-start flex-centered">
            <ion-icon
              [name]="feature.icon"
              style="font-size: 25px; margin-right: 5px"
            ></ion-icon>
            {{ feature.name }}
          </ion-label>
        </p>
      </section>

      <section>
        <h1>Абонаментни планове</h1>

        <div class="overlay">
          <mpg-subscription-plan-card
            [type]="SubscriptionPlanType.ESSENTIALS"
          ></mpg-subscription-plan-card>
          <mpg-subscription-plan-card
            [type]="SubscriptionPlanType.ADVANCED"
          ></mpg-subscription-plan-card>
          <mpg-subscription-plan-card
            [type]="SubscriptionPlanType.PREMIUM"
          ></mpg-subscription-plan-card>
        </div>
      </section>

      <div
        (click)="handleStartFree()"
        class="cta ion-activatable overflow-hidden"
      >
        <ion-ripple-effect></ion-ripple-effect>
        Започни безплатно
      </div>

      <!--            <mpg-card color="primary"  title="Функционалности">-->
      <!--              <ion-item *ngFor="let feature of features">-->
      <!--                <ion-label class="ion-text-wrap ion-text-center flex-centered">-->
      <!--                  <ion-icon-->
      <!--                    [name]="feature.icon"-->
      <!--                    style="font-size: 25px; margin-right: 5px"-->
      <!--                  ></ion-icon>-->
      <!--                  {{ feature.name }}-->
      <!--                </ion-label>-->
      <!--              </ion-item>-->
      <!--            </mpg-card>-->
      <!--            <div>-->
      <!--              <ion-item>-->
      <!--                <ion-label class="ion-text-center">-->
      <!--                  <ion-chip (click)="handleInstagramClick()" class="dark">-->
      <!--                    <ion-icon color="light" name="logo-instagram"></ion-icon>-->
      <!--                    <ion-label>sashopetrov_</ion-label>-->
      <!--                  </ion-chip>-->
      <!--                </ion-label>-->
      <!--              </ion-item>-->
      <!--            </div>-->
    </ion-col>
  </ion-row>

  <footer>
    <p>
      Имаш въпрос? Свържи се с мен:
      <a href="mailto:<EMAIL>"
        >aleksandar&#64;myprogressguru.com</a
      >
    </p>
    <a href="https://myprogressguru.com/legal?segment=terms-and-conditions"
      >Условия за ползване</a
    >
    |
    <a href="https://myprogressguru.com/legal?segment=refund-policy"
      >Политика за връщане</a
    >
    |
    <a href="https://myprogressguru.com/legal?segment=privacy-policy"
      >Политика за поверителност</a
    >
    |
    <a href="https://myprogressguru.com/legal?segment=delivery-terms"
      >Условия за доставка</a
    >
    <p>© САШКО-МПГ ЕООД. Всички права запазени.</p>
  </footer>
</ion-content>

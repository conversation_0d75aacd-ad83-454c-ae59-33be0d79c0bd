import { Component, OnInit } from '@angular/core';
import {
  Locale,
  LocalizationService,
  ModalService,
} from '../../../shared/services';
import Plyr from 'plyr';
import { SubscriptionPlanType } from 'src/app/payments/enumerations';
import { StartFreeInstructionsModalComponent } from '../../components/start-free-instructions-modal/start-free-instructions-modal.component';

@Component({
  selector: 'mpg-introduction',
  templateUrl: './introduction.page.html',
  styleUrls: ['./introduction.page.scss'],
})
export class IntroductionPage implements OnInit {
  player: Plyr;
  features: { name: string; icon: string }[] = [
    { name: 'Промоции по магазините', icon: 'pricetags' },
    { name: 'Следене на теглото', icon: 'analytics' },
    { name: 'Оценка на съня', icon: 'moon' },
    { name: 'Тренировъчен график', icon: 'calendar' },
    { name: 'Хра<PERSON><PERSON><PERSON>е<PERSON>ен дневник', icon: 'fast-food' },
    { name: 'Баркод скенер', icon: 'barcode' },
    { name: 'Различни фитнес зали', icon: 'location' },
    { name: 'Бележки', icon: 'document-text' },
    { name: 'Визуален прогрес', icon: 'accessibility' },
    { name: 'Discord общество', icon: 'logo-discord' },
    { name: 'Интеграция с Whoop', icon: 'construct' },
  ];

  SubscriptionPlanType = SubscriptionPlanType;

  constructor(
    private localizationService: LocalizationService,
    private modalService: ModalService,
  ) {}

  ngOnInit() {
    this.localizationService.setLang(Locale.BG, false);
  }

  confirmHandler() {
    this.player.play();
    this.player.fullscreen.enter();
  }

  handleStartFree() {
    this.modalService
      .create(
        {
          component: StartFreeInstructionsModalComponent,
        },
        'xl',
      )
      .subscribe();
  }
}

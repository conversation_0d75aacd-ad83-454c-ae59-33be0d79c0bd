import { Component } from '@angular/core';

@Component({
  selector: 'mpg-lottie-demo',
  template: `
    <ion-header [translucent]="true">
      <ion-toolbar>
        <ion-title>Lottie Animation Demo</ion-title>
        <ion-buttons slot="start">
          <ion-back-button defaultHref="/"></ion-back-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content [fullscreen]="true">
      <ion-header collapse="condense">
        <ion-toolbar>
          <ion-title size="large">Lottie Demo</ion-title>
        </ion-toolbar>
      </ion-header>

      <div class="demo-container">
        <mpg-lottie-test></mpg-lottie-test>
      </div>
    </ion-content>
  `,
  styles: [`
    .demo-container {
      padding: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: calc(100vh - 200px);
    }
  `]
})
export class LottieDemoPage {
  constructor() { }
}

<div
  *ngIf="soundUrl ? !hasUserClicked : false"
  class="h-full flex-column-centered"
>
  <ion-text color="dark"
    ><h2 class="bolder ion-padding">
      {{ "notifications.special" | translate }}
    </h2>
  </ion-text>
  <ion-button (click)="handleUserClick()"
    >{{ "buttons.of-course" | translate }}
  </ion-button>
</div>

<mpg-modal-layout
  *ngIf="soundUrl ? hasUserClicked : true"
  [iconButtons]="!isCampaign ? iconButtons : undefined"
  [title]="notification.title"
>
  <div [ngClass]="{ 'h-full': !photoUrl }">
    <ion-text *ngIf="notification.body"
      ><h5 class="ion-padding-horizontal ion-margin-bottom">
        {{ notification.body }}
      </h5></ion-text
    >
    <video *ngIf="videoUrl" [src]="videoUrl" mpgVideoPlayer></video>
    <div *ngIf="isCampaign" class="modal-buttons-container">
      <ion-button
        (click)="handleBlackFriday()"
        class="ion-margin"
        color="primary"
      >
        Виж промоциите
      </ion-button>
    </div>
  </div>
  <ion-img *ngIf="photoUrl" [src]="photoUrl"></ion-img>
</mpg-modal-layout>

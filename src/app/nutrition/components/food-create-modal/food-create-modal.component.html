<mpg-modal-layout [button]="modalButton" title="nutrition.create-food">
  <mpg-loading-spinner
    [fullScreen]="false"
    [isLoading]="isLoading"
  ></mpg-loading-spinner>
  <form *ngIf="!isLoading" [formGroup]="foodFormGroup">
    <ion-item *ngIf="!food">
      <ion-input
        [label]="'nutrition.brand-name' | translate"
        formControlName="brandName"
        labelPlacement="floating"
        type="text"
      ></ion-input>
    </ion-item>
    <ion-item>
      <ion-input
        [label]="'nutrition.food-name' | translate"
        formControlName="name"
        labelPlacement="floating"
        type="text"
      ></ion-input>
    </ion-item>
    <ng-container formGroupName="nutritionInfo">
      <ion-item>
        <ion-input
          [label]="'nutrition.fat-per-100g' | translate"
          formControlName="fat"
          labelPlacement="floating"
          type="number"
        ></ion-input>
      </ion-item>
      <ion-item>
        <ion-input
          [label]="'nutrition.carbs-per-100g' | translate"
          formControlName="carbs"
          labelPlacement="floating"
          type="number"
        ></ion-input>
      </ion-item>
      <ion-item>
        <ion-input
          [label]="'nutrition.protein-per-100g' | translate"
          formControlName="protein"
          labelPlacement="floating"
          type="number"
        ></ion-input>
      </ion-item>
    </ng-container>
  </form>
</mpg-modal-layout>

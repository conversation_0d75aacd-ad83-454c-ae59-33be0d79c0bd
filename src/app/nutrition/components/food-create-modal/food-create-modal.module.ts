import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { FoodCreateModalComponent } from './food-create-modal.component';
import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';
import { TranslateModule } from '@ngx-translate/core';
import { EntitySelectModule } from '../../../shared/components/entity-select/entity-select.module';
import { LoadingSpinnerComponentModule } from '../../../shared/components/loading-spinner/loading-spinner.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ModalLayoutModule,
    TranslateModule,
    EntitySelectModule,
    ReactiveFormsModule,
    LoadingSpinnerComponentModule,
  ],
  declarations: [FoodCreateModalComponent],
  exports: [FoodCreateModalComponent],
})
export class FoodCreateModalModule {}

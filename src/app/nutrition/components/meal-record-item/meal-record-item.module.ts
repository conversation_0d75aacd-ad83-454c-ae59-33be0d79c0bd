import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { MealRecordItemComponent } from './meal-record-item.component';
import { TranslateModule } from '@ngx-translate/core';
import { TimeFormatPipeModule } from '../../../shared/pipes/time-format/time-format-pipe.module';
import { FoodItemModule } from '../food-item/food-item.module';
import { NutritionInfoChipsModule } from '../nutrition-info-chips/nutrition-info-chips.module';
import { ScreenshotButtonModule } from '../../../shared/components/screenshot-button/screenshot-button.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ReactiveFormsModule,
    TranslateModule,
    TimeFormatPipeModule,
    FoodItemModule,
    NutritionInfoChipsModule,
    ScreenshotButtonModule,
  ],
  declarations: [MealRecordItemComponent],
  exports: [MealRecordItemComponent],
})
export class MealRecordItemModule {}

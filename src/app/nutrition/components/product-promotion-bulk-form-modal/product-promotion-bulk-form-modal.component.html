<mpg-modal-layout [button]="modalButton" title="nutrition.add-promotions">
  <mpg-loading-spinner
    [fullScreen]="false"
    [isLoading]="isLoading"
  ></mpg-loading-spinner>
  <ng-container *ngIf="!isLoading">
    <form [formGroup]="initialFormGroup">
      <ion-item>
        <ion-select
          class="centered"
          formControlName="storeName"
          interface="popover"
          labelPlacement="stacked"
        >
          <ion-select-option
            *ngFor="let store of stores; let first = first"
            [value]="store"
            >{{ store }}
          </ion-select-option>
        </ion-select>
      </ion-item>
      <ion-item>
        <ion-label>{{ "start-date" | translate }}</ion-label>
        <ion-datetime-button datetime="startDate"></ion-datetime-button>
      </ion-item>
      <ion-item>
        <ion-label>{{ "end-date" | translate }}</ion-label>
        <ion-datetime-button datetime="endDate"></ion-datetime-button>
      </ion-item>
      <ion-modal [keepContentsMounted]="true">
        <ng-template>
          <ion-datetime
            formControlName="startDate"
            id="startDate"
            presentation="date"
          ></ion-datetime>
        </ng-template>
      </ion-modal>
      <ion-modal [keepContentsMounted]="true">
        <ng-template>
          <ion-datetime
            [minuteValues]="[0, 15, 30, 45]"
            formControlName="endDate"
            id="endDate"
            presentation="date"
          ></ion-datetime>
        </ng-template>
      </ion-modal>
    </form>
    <div
      class="modal-buttons-container ion-justify-content-center ion-margin-bottom"
    >
      <ion-fab-button
        (click)="imagesInput.click()"
        [disabled]="initialFormGroup?.invalid"
        class="ion-margin-top"
      >
        <ion-icon name="camera"></ion-icon>
      </ion-fab-button>
      <ion-fab-button
        (click)="handleAIRecognition()"
        *ngIf="photosUploaded"
        class="ion-margin-top ion-margin-start"
        color="secondary"
      >
        <ion-icon name="logo-ionitron"></ion-icon>
      </ion-fab-button>
      <input
        #imagesInput
        (change)="handleImagesSelect($event)"
        [multiple]="true"
        accept="image/*"
        class="invisible"
        type="file"
      />
    </div>
    <input
      #csvInput
      (change)="handleCSVSelect($event)"
      accept=".csv"
      class="invisible"
      type="file"
    />
    <form
      *ngFor="let formGroup of formArray?.controls; let index = index"
      [formGroup]="getFormGroup(formGroup)"
    >
      <ion-item lines="none">
        <ion-thumbnail
          (click)="
            showPhoto(
              formGroupPhotoMap[index] ||
                'https://ionicframework.com/docs/img/demos/thumbnail.svg'
            )
          "
          slot="start"
        >
          <ion-img
            [src]="
              formGroupPhotoMap[index] ||
              'https://ionicframework.com/docs/img/demos/thumbnail.svg'
            "
          />
        </ion-thumbnail>
        <ion-input formControlName="productName"></ion-input>
      </ion-item>
      <div
        class="flex-centered flex-wrap w-full white-background ion-padding-bottom mt"
      >
        <ion-input
          formControlName="discountPrice"
          label="Discount price"
          labelPlacement="floating"
          type="number"
        ></ion-input>
        <ion-input
          formControlName="regularPrice"
          label="Regular price"
          labelPlacement="floating"
          type="number"
        ></ion-input>
        <ion-input
          formControlName="discountPercentage"
          label="Discount percentage"
          labelPlacement="floating"
          type="number"
        ></ion-input>
      </div>

      <input
        #imageInput
        (change)="handleImageSelect($event, formGroup, index)"
        accept="image/*"
        class="invisible"
        type="file"
      />
    </form>
  </ng-container>
</mpg-modal-layout>

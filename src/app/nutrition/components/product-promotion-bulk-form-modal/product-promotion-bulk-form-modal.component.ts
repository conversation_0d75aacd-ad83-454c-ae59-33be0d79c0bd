import { Component, OnInit } from '@angular/core';
import {
  AlertService,
  FileService,
  ModalService,
  ToastService,
} from '../../../shared/services';
import { ProductPromotionService } from '../../services';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import Papa from 'papaparse';
import { ProductPromotionStoreName } from '../../enumerations';
import { environment } from '../../../../environments/environment';
import { Button } from '../../../shared/models';
import { map } from 'rxjs/operators';
import { filter, forkJoin } from 'rxjs';
import { WebSocketService } from '../../../notifications/services';
import { ProductPromotionAIResult } from '../../models';
import { WebSocketAction } from '../../../notifications/enumerations';
import { MPGValidators } from '../../../shared/validators/validators';

@Component({
  selector: 'mpg-product-promotion-bulk-form-modal',
  templateUrl: './product-promotion-bulk-form-modal.component.html',
  styleUrls: ['./product-promotion-bulk-form-modal.component.scss'],
})
export class ProductPromotionBulkFormModalComponent implements OnInit {
  formArray: FormArray;
  formGroupPhotoMap: Record<number, string> = {};
  isLoading = false;
  initialFormGroup: FormGroup;

  modalButton: Button = {
    label: 'buttons.submit',
    handler: () => {
      if (this.formArray.invalid) {
        return;
      }

      this.isLoading = true;
      this.productPromotionService
        .bulkCreate(this.formArray.value)
        .subscribe(() => {
          this.isLoading = false;
          this.toastService.showInfoToast(
            'nutrition.create-product-promotions-success',
          );
          this.modalService.closeTopModal(true);
        });
    },
    disabled: () => {
      return !this.formArray || this.formArray.invalid || this.isLoading;
    },
  };
  stores = Object.values(ProductPromotionStoreName);

  constructor(
    private modalService: ModalService,
    private fb: FormBuilder,
    private productPromotionService: ProductPromotionService,
    private toastService: ToastService,
    private alertService: AlertService,
    private fileService: FileService,
    private webSocketService: WebSocketService,
  ) {}

  get photosUploaded(): boolean {
    return (
      Object.values(this.formGroupPhotoMap)?.length === this.formArray?.length
    );
  }

  ngOnInit(): void {
    this.webSocketService
      .on<ProductPromotionAIResult>(
        WebSocketAction.PRODUCT_PROMOTION_RECOGNITION,
      )
      .subscribe((result) => {
        const formGroup = this.formArray.controls[result.index] as FormGroup;
        formGroup.patchValue({
          productName: result.productName,
          discountPrice: result.discountPrice,
          regularPrice: result.regularPrice,
          discountPercentage: result.discountPercentage,
        });
        const fullFormGroups = this.formArray.controls.filter((formGroup) => {
          return formGroup.get('productName').value;
        }).length;

        this.toastService.showInfoToast(
          `${fullFormGroups} out of ${this.formArray.length} promotions recognized`,
        );
      });

    this.initialFormGroup = this.fb.group(
      {
        storeName: [ProductPromotionStoreName.KAUFLAND, Validators.required],
        startDate: [null, Validators.required],
        endDate: [null, Validators.required],
      },
      {
        validators: MPGValidators.endDateAfterStartDate('endDate', 'startDate'),
      },
    );
  }

  handleCSVSelect(event: any) {
    Papa.parse(event.target.files[0], {
      header: true,
      skipEmptyLines: true,
      complete: (result) => {
        const promotions = result.data as Record<string, string>[];
        this.createFormGroups(promotions);
      },
    });
  }

  getFormGroup(formGroup: AbstractControl) {
    return formGroup as FormGroup;
  }

  handleImageSelect(
    event: Event,
    formGroupControl: AbstractControl,
    index: number,
  ) {
    const formGroup = this.getFormGroup(formGroupControl);

    this.uploadImage(event, formGroup).subscribe((storageObject) => {
      if (storageObject.progress === 100) {
        this.formGroupPhotoMap[index] = storageObject.url;
      }
    });
  }

  handleTemplateDownload() {
    window.open('/assets/templates/promotions-bulk-template.xlsx');
  }

  handleImagesSelect(event: any) {
    const imageFiles = Array.from(event.target.files);
    this.formArray = this.createEmptyFormArray(imageFiles.length);
    const formGroups = this.formArray.controls as FormGroup[];

    if (imageFiles.length !== formGroups.length) {
      this.alertService.createErrorAlert(
        'Number of images should match the number of promotions',
      );
      return;
    }

    const uploadObservables = imageFiles.map((file, index) => {
      return this.uploadImage(
        { target: { files: [file] } },
        formGroups[index],
      ).pipe(
        filter((s) => s.progress === 100),
        map((s) => [s.url, index]),
      );
    });

    forkJoin(uploadObservables).subscribe((results) => {
      results.forEach(([url, index]) => {
        this.formGroupPhotoMap[index] = url;
      });
    });
  }

  showPhoto(url: string) {
    this.modalService.showPhoto(url);
  }

  handleAIRecognition() {
    this.productPromotionService
      .bulkAiCreate(
        Object.keys(this.formGroupPhotoMap).map((index) => {
          return {
            index: +index,
            photoUrl: this.formGroupPhotoMap[index],
          };
        }),
      )
      .subscribe();
  }

  private uploadImage(event: any, formGroup: FormGroup<any>) {
    return this.fileService.uploadFile(
      event,
      this.productPromotionService.getPhotoUploadUrl,
      environment.NUTRITION_SERVICE_API_URL,
      formGroup.controls.photoId as FormControl,
    );
  }

  private validateString(value: string, prop: string): string {
    if (!value) {
      throw new Error('All rows should have a value with header: ' + prop);
    }

    return value;
  }

  private validateStore(value: string): string {
    if (
      !Object.values(ProductPromotionStoreName).includes(
        value as ProductPromotionStoreName,
      )
    ) {
      throw new Error(
        'Invalid store name. Valid store names: ' +
          Object.values(ProductPromotionStoreName).join(', '),
      );
    }

    return value;
  }

  private validateDate(value: string, prop: string): string {
    const date = this.validateString(value, prop);

    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(date)) {
      throw new Error(
        'Invalid date format in column: ' + prop + '. Use YYYY-MM-DD',
      );
    }

    return date;
  }

  private validateNumber(
    value: string,
    prop: string,
    required = false,
  ): number {
    if (!value && !required) {
      return null;
    }

    const parsedNumber = parseFloat(value);
    if (isNaN(parsedNumber)) {
      throw new Error('Invalid number format in column: ' + prop);
    }

    return parsedNumber;
  }

  private createFormGroups(promotions: Record<string, string>[]) {
    try {
      const formGroups = promotions.map((promotion) => {
        if (promotion.startDate > promotion.endDate) {
          throw new Error(
            `Start date (${promotion.startDate}) must be before or equal to end date (${promotion.endDate})`,
          );
        }

        return this.fb.group({
          productName: [
            this.validateString(promotion.productName, 'productName'),
            Validators.required,
          ],
          storeName: [
            this.validateStore(promotion.storeName),
            Validators.required,
          ],
          startDate: [
            this.validateDate(promotion.startDate, 'startDate'),
            Validators.required,
          ],
          endDate: [
            this.validateDate(promotion.endDate, 'endDate'),
            Validators.required,
          ],
          discountPrice: [
            this.validateNumber(promotion.discountPrice, 'discountPrice'),
          ],
          regularPrice: [
            this.validateNumber(promotion.regularPrice, 'regularPrice'),
          ],
          discountPercentage: [
            this.validateNumber(
              promotion.discountPercentage,
              'discountPercentage',
            ),
          ],
          photoId: [null, Validators.required],
        });
      });

      this.formArray = this.fb.array(formGroups);
    } catch (error) {
      this.alertService.createErrorAlert(error);
    }
  }

  private createEmptyFormArray(count: number): FormArray {
    const formGroups = Array.from({ length: count }).map(() => {
      return this.fb.group({
        productName: ['', Validators.required],
        storeName: [
          this.initialFormGroup.controls.storeName.value,
          Validators.required,
        ],
        startDate: [
          this.initialFormGroup.controls.startDate.value,
          Validators.required,
        ],
        endDate: [
          this.initialFormGroup.controls.endDate.value,
          Validators.required,
        ],
        discountPrice: [null],
        regularPrice: [null],
        discountPercentage: [null],
        photoId: [null, Validators.required],
      });
    });

    return this.fb.array(formGroups);
  }
}

import { Component, OnInit } from '@angular/core';
import {
  MOCK_PROOF_NUTRITION_PRODUCTS,
  ProofNutritionProduct,
} from '../../models';
import { ToastService } from '../../../shared/services';
import { PartnershipConfig } from '../../../shared/components/partnership-header/partnership-header.model';

@Component({
  selector: 'mpg-proof-nutrition-products',
  templateUrl: './proof-nutrition-products.component.html',
  styleUrls: ['./proof-nutrition-products.component.scss'],
})
export class ProofNutritionProductsComponent implements OnInit {
  products: ProofNutritionProduct[] = [];

  partnershipConfig: PartnershipConfig = {
    logoSrc: '/assets/images/supplements/proof.png',
    logoAlt: 'Proof Nutrition',
    title:
      'My Progress Guru си партнира с Proof Nutrition - научно-обосновани хранителни добавки!',
    description:
      'Подкрепете развитието на нашето приложение и вземете 10% отстъпка с промокод MPG10 при следващата си поръчка от Proof Nutrition!',
    promoCode: 'MPG10',
    storeUrl: 'https://proofnutrition.eu',
    copyButtonText: 'Копирай кода',
    visitStoreButtonText: 'Посети магазина',
  };

  constructor(private toastService: ToastService) {}

  ngOnInit() {
    this.loadProducts();
  }

  openProduct(productUrl: string) {
    window.open(productUrl, '_blank');
  }

  getStarArray(rating: number): boolean[] {
    const stars = [];
    for (let i = 1; i < 5; i++) {
      stars.push(i <= Math.floor(rating));
    }
    return stars;
  }

  hasHalfStar(rating: number): boolean {
    return rating % 1 >= 0.5;
  }

  private loadProducts() {
    this.products = MOCK_PROOF_NUTRITION_PRODUCTS.map((product) => ({
      ...product,
      discountPrice: Math.round(product.price * 0.9 * 100) / 100, // 10% discount
      discountPercentage: 10,
    }));
  }
}

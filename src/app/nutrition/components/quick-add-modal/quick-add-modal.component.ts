import { Component, Input, OnInit } from '@angular/core';
import { But<PERSON> } from '../../../shared/models';
import { ModalService } from '../../../shared/services';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { combineLatestWith, startWith } from 'rxjs';
import { FoodRecord } from '../../models';

@Component({
  selector: 'mpg-quick-add-modal',
  templateUrl: './quick-add-modal.component.html',
  styleUrls: ['./quick-add-modal.component.scss'],
})
export class QuickAddModalComponent implements OnInit {
  @Input() foodRecord: FoodRecord;

  quickAddFormGroup: FormGroup;
  modalButton: Button = {
    label: 'buttons.save',
    handler: () => {
      if (this.quickAddFormGroup.invalid) {
        return;
      }

      this.modalService.closeTopModal(this.quickAddFormGroup.value);
    },
    disabled: () => this.quickAddFormGroup.invalid,
  };

  constructor(
    private modalService: ModalService,
    private fb: FormBuilder,
    private translateService: TranslateService,
  ) {}

  ngOnInit(): void {
    const defaultNutritionInfo = {
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0,
      saturatedFat: 0,
      monounsaturatedFat: 0,
      polyunsaturatedFat: 0,
      transFat: 0,
      sugar: 0,
      fiber: 0,
      glycemicIndex: 0,
      cholesterol: 0,
      sodium: 0,
      potassium: 0,
      iron: 0,
      calcium: 0,
      vitaminA: 0,
      vitaminB: 0,
      vitaminC: 0,
    };

    const nutritionInfo =
      this.foodRecord?.nutritionInfo || defaultNutritionInfo;
    const name =
      this.foodRecord?.name ||
      this.translateService.instant('nutrition.quick-add') ||
      null;
    const metricServingAmount = this.foodRecord?.metricServingAmount || 1;
    const metricServingUnit = this.foodRecord?.metricServingUnit || '';

    const nutritionInfoFormGroup = this.fb.group({
      calories: [
        nutritionInfo.calories,
        [Validators.required, Validators.min(0)],
      ],
      protein: [
        nutritionInfo.protein,
        [Validators.required, Validators.min(0)],
      ],
      carbs: [nutritionInfo.carbs, [Validators.required, Validators.min(0)]],
      fat: [nutritionInfo.fat, [Validators.required, Validators.min(0)]],
      saturatedFat: [nutritionInfo.saturatedFat || 0],
      monounsaturatedFat: [nutritionInfo.monounsaturatedFat || 0],
      polyunsaturatedFat: [nutritionInfo.polyunsaturatedFat || 0],
      transFat: [nutritionInfo.transFat || 0],
      sugar: [nutritionInfo.sugar || 0],
      fiber: [nutritionInfo.fiber || 0],
      glycemicIndex: [nutritionInfo.glycemicIndex || 0],
      cholesterol: [nutritionInfo.cholesterol || 0],
      sodium: [nutritionInfo.sodium || 0],
      potassium: [nutritionInfo.potassium || 0],
      iron: [nutritionInfo.iron || 0],
      calcium: [nutritionInfo.calcium || 0],
      vitaminA: [nutritionInfo.vitaminA || 0],
      vitaminB: [nutritionInfo.vitaminB || 0],
      vitaminC: [nutritionInfo.vitaminC || 0],
    });

    this.quickAddFormGroup = this.fb.group({
      isQuickAdded: [true],
      name: [name, Validators.required],
      metricServingAmount: [
        metricServingAmount,
        [Validators.required, Validators.min(1)],
      ],
      metricServingUnit: [metricServingUnit],
      nutritionInfo: nutritionInfoFormGroup,
    });

    const protein = nutritionInfoFormGroup.controls.protein.valueChanges;
    const carbs = nutritionInfoFormGroup.controls.carbs.valueChanges;
    const fat = nutritionInfoFormGroup.controls.fat.valueChanges;

    protein
      .pipe(
        startWith(0),
        combineLatestWith(carbs.pipe(startWith(0)), fat.pipe(startWith(0))),
      )
      .subscribe(([proteinValue, carbsValue, fatValue]) => {
        const calories = Math.round(
          (proteinValue || 0) * 4 + (carbsValue || 0) * 4 + (fatValue || 0) * 9,
        );

        nutritionInfoFormGroup.controls.calories.setValue(calories);
      });
  }
}

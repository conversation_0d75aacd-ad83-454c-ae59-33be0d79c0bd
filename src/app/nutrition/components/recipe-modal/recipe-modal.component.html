<mpg-modal-layout [button]="modalButton" [title]="recipe.recipeName">
  <ion-card-content class="light-background ion-no-padding">
    <ion-chip *ngIf="recipe.rating" class="light rating">
      <ion-icon color="dark" name="star"></ion-icon>
      <ion-label>{{ recipe.rating }}/5</ion-label>
    </ion-chip>
    <ion-img
      (click)="handleImageClick()"
      [src]="recipe.recipeImages.recipeImage[0]"
    ></ion-img>
    <ion-item class="ion-text-center">
      <ion-label class="ion-text-wrap"
        >{{ recipe.recipeDescription }}
      </ion-label>
    </ion-item>
    <ion-item class="no-end-padding">
      <ion-row class="w-full no-bottom-padding">
        <ion-col size="12">
          <ion-label class="ion-text-center"
            >{{ recipe.gramsPerPortion | number: "1.0-0" }} g. portion
          </ion-label>
        </ion-col>
        <ion-col class="flex-centered no-top-padding" size="12">
          <ion-chip class="dark">
            <ion-icon color="light" name="flame"></ion-icon>
            <ion-label
              >{{ recipe.servingSizes.serving.calories | number: "1.0-0" }}
            </ion-label>
          </ion-chip>
          <ion-chip class="primary">
            <ion-label
              >{{ recipe.servingSizes.serving.protein | number: "1.0-0" }} P
            </ion-label>
          </ion-chip>
          <ion-chip class="secondary">
            <ion-label
              >{{ recipe.servingSizes.serving.carbohydrate | number: "1.0-0" }}
              C
            </ion-label>
          </ion-chip>
          <ion-chip class="warning">
            <ion-label
              >{{ recipe.servingSizes.serving.fat | number: "1.0-0" }} F
            </ion-label>
          </ion-chip>
        </ion-col>
      </ion-row>
    </ion-item>
    <ion-item class="ion-text-center no-end-padding">
      <ion-row class="w-full">
        <ion-col class="flex-space-around" size="12">
          <ion-label>Prep</ion-label>
          <ion-chip>
            <ion-icon color="dark" name="alarm"></ion-icon>
            <ion-label>{{ recipe.preparationTimeMin }} min</ion-label>
          </ion-chip>
          <ion-label>Cooking</ion-label>
          <ion-chip>
            <ion-icon color="dark" name="alarm"></ion-icon>
            <ion-label>{{ recipe.cookingTimeMin }} min</ion-label>
          </ion-chip>
        </ion-col>
      </ion-row>
    </ion-item>
    <ion-item class="ion-text-center no-end-padding">
      <ion-row class="w-full">
        <ion-col class="flex-centered" size="12">
          <ion-chip *ngFor="let recipeType of recipe.recipeTypes.recipeType">
            <ion-label>{{ recipeType }}</ion-label>
          </ion-chip>
        </ion-col>
      </ion-row>
    </ion-item>
    <div class="ion-padding no-bottom-padding">
      <ion-text class="bold border-bottom ingredients"
        >Ingredients for {{ recipe.numberOfServings }} portions
      </ion-text>

      <ul>
        <li *ngFor="let ingredient of recipe.ingredients.ingredient">
          {{ ingredient.ingredientDescription }}
        </li>
      </ul>
    </div>

    <div class="ion-padding no-top-padding">
      <ion-text class="bold border-bottom ingredients">Directions</ion-text>

      <ol>
        <li *ngFor="let direction of recipe.directions.direction">
          {{ direction.directionDescription }}
        </li>
      </ol>
    </div>
  </ion-card-content>
</mpg-modal-layout>

<mpg-modal-layout [button]="modalButton" title="payments.billing-information">
  <form
    *ngIf="billingInformationFormGroup"
    [formGroup]="billingInformationFormGroup"
  >
    <ion-item>
      <ion-label class="ion-text-center">
        {{ "payments.billing-information-form.cyrillic-helper" | translate }}
      </ion-label>
    </ion-item>
    <mpg-entity-select
      *ngIf="showAdminInputs"
      [formatter]="formatter"
      [label]="'auth.user' | translate"
      [searchObservableGetter]="searchObservableGetter"
      entityIdentifierProp="trainee.id"
      formControlName="userId"
    >
    </mpg-entity-select>
    <ion-item>
      <ion-select
        [label]="'type' | translate"
        [mode]="mode"
        class="centered"
        formControlName="type"
        interface="popover"
        labelPlacement="stacked"
      >
        <ion-select-option
          *ngFor="let type of types; let first = first"
          [value]="type"
          >{{ "payments.billing-information-form.type." + type | translate }}
        </ion-select-option>
      </ion-select>
    </ion-item>
    <ng-container *ngIf="selectedType === BillingInformationType.COMPANY">
      <ion-item>
        <ion-input
          [label]="'payments.billing-information-form.company-name' | translate"
          formControlName="companyName"
          labelPlacement="floating"
          type="text"
        ></ion-input>
      </ion-item>
      <ion-item>
        <ion-input
          [label]="
            'payments.billing-information-form.unified-id-code' | translate
          "
          formControlName="unifiedIdCode"
          labelPlacement="floating"
          type="text"
        ></ion-input>
      </ion-item>
      <ion-item>
        <ion-input
          [label]="'payments.billing-information-form.vat-number' | translate"
          formControlName="vatNumber"
          labelPlacement="floating"
          type="text"
        ></ion-input>
      </ion-item>
    </ng-container>
    <ion-item *ngIf="showAdminInputs">
      <ion-input
        [label]="'payments.billing-information-form.email' | translate"
        formControlName="email"
        labelPlacement="floating"
        type="text"
      ></ion-input>
    </ion-item>
    <ion-item>
      <ion-input
        [label]="'payments.billing-information-form.first-name' | translate"
        formControlName="firstName"
        labelPlacement="floating"
        placeholder="Иван"
        type="text"
      ></ion-input>
    </ion-item>
    <ion-item>
      <ion-input
        [label]="'payments.billing-information-form.middle-name' | translate"
        formControlName="middleName"
        labelPlacement="floating"
        placeholder="Иванов"
        type="text"
      ></ion-input>
    </ion-item>
    <ion-item>
      <ion-input
        [label]="'payments.billing-information-form.last-name' | translate"
        formControlName="lastName"
        labelPlacement="floating"
        placeholder="Иванов"
        type="text"
      ></ion-input>
    </ion-item>
    <ion-item>
      <ion-input
        [label]="'payments.billing-information-form.city' | translate"
        formControlName="city"
        labelPlacement="floating"
        placeholder="София"
        type="text"
      ></ion-input>
    </ion-item>
    <ion-item>
      <ion-select
        [label]="'payments.billing-information-form.country' | translate"
        [mode]="mode"
        class="centered"
        formControlName="countryId"
        interface="popover"
        labelPlacement="stacked"
      >
        <ion-select-option
          *ngFor="let country of countries; let first = first"
          [value]="country.id"
          >{{ (locale$ | async) === "bg" ? country.nameBg : country.name }}
        </ion-select-option>
      </ion-select>
    </ion-item>
    <ion-item>
      <ion-input
        [label]="'payments.billing-information-form.address' | translate"
        formControlName="address"
        labelPlacement="floating"
        placeholder="ул. Цветна градина 1"
        type="text"
      ></ion-input>
    </ion-item>
    <ion-item>
      <ion-input
        [label]="'payments.billing-information-form.postal-code' | translate"
        formControlName="postalCode"
        labelPlacement="floating"
        type="text"
      ></ion-input>
    </ion-item>
    <ion-item>
      <ion-input
        [label]="
          ('payments.billing-information-form.phone-number' | translate) +
          ' ' +
          ('payments.billing-information-form.phone-number-helper'
            | translate
            | lowercase)
        "
        formControlName="phoneNumber"
        labelPlacement="floating"
        type="text"
      ></ion-input>
    </ion-item>
  </form>
</mpg-modal-layout>

<ion-item
  *ngIf="(subscriptionPlan$ | async) === SubscriptionPlanType.ESSENTIALS"
  [ngClass]="{ standalone: standalone }"
>
  <ion-label class="ion-text-center">
    <ion-chip (click)="handlePaidFeature()">
      <ion-label
        >{{ "payments.paid-feature.ESSENTIALS.history-limit" | translate }}
      </ion-label>
      <ion-icon name="close-circle"></ion-icon>
    </ion-chip>
  </ion-label>
</ion-item>

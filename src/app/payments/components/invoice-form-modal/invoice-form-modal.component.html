<mpg-modal-layout [button]="modalButton" title="payments.billing-information">
  <form *ngIf="invoiceFormGroup" [formGroup]="invoiceFormGroup">
    <ion-item>
      <ion-input
        [label]="'payments.invoice-form.number' | translate"
        formControlName="number"
        labelPlacement="floating"
        type="number"
      ></ion-input>
    </ion-item>
    <ion-item>
      <ion-label>{{ "date" | translate }}</ion-label>
      <ion-datetime-button datetime="date"></ion-datetime-button>
    </ion-item>
    <ion-modal [keepContentsMounted]="true">
      <ng-template>
        <ion-datetime
          [locale]="locale$ | async"
          formControlName="date"
          id="date"
          presentation="date"
        ></ion-datetime>
      </ng-template>
    </ion-modal>
    <ion-item>
      <ion-input
        [label]="'payments.invoice-form.product-name' | translate"
        formControlName="productName"
        labelPlacement="floating"
        type="text"
      ></ion-input>
    </ion-item>
    <ion-item>
      <ion-input
        [label]="'payments.invoice-form.amount' | translate"
        formControlName="amount"
        labelPlacement="floating"
        type="number"
      ></ion-input>
    </ion-item>
    <ion-item>
      <ion-select
        [label]="'payments.currency' | translate"
        [mode]="mode"
        class="centered"
        formControlName="currency"
        interface="popover"
        labelPlacement="floating"
      >
        <ion-select-option
          *ngFor="let currency of currencies"
          [value]="currency"
        >
          {{ currency }}
        </ion-select-option>
      </ion-select>
    </ion-item>
    <ion-item>
      <ion-input
        [label]="'payments.invoice-form.payment-reference' | translate"
        formControlName="paymentReference"
        labelPlacement="floating"
        type="text"
      ></ion-input>
    </ion-item>
    <ion-item>
      <ion-select
        [label]="'payments.invoice-form.payment-method' | translate"
        [mode]="mode"
        class="centered"
        formControlName="paymentMethod"
        interface="popover"
        labelPlacement="floating"
      >
        <ion-select-option
          *ngFor="let currency of paymentMethods"
          [value]="currency"
        >
          {{ currency }}
        </ion-select-option>
      </ion-select>
    </ion-item>
    <mpg-entity-select
      (onNewEntity)="handleNewBillingInformation($event)"
      [formatter]="formatter"
      [initialDisplayValue]="getBillingInformationDisplayValue()"
      [label]="'payments.billing-information' | translate"
      [searchObservableGetter]="searchObservableGetter"
      formControlName="billingInformationId"
    >
    </mpg-entity-select>
  </form>
</mpg-modal-layout>

<mpg-modal-layout
  [button]="modalButton"
  [title]="subscription ? 'buttons.edit' : 'buttons.create'"
>
  <form [formGroup]="subscriptionFormGroup">
    <ion-item>
      <ion-select
        formControlName="subscriptionPlanId"
        interface="popover"
        label="Subscription Plan"
      >
        <ion-select-option
          *ngFor="let plan of subscriptionPlans"
          [value]="plan.id"
        >
          {{ plan.price }} {{ plan.currency }}
        </ion-select-option>
      </ion-select>
    </ion-item>
    <ion-item>
      <ion-label>{{ "payments.trial-end" | translate }}</ion-label>
      <ion-datetime-button datetime="date"></ion-datetime-button>
    </ion-item>
    <ion-modal [keepContentsMounted]="true">
      <ng-template>
        <ion-datetime
          [locale]="locale$ | async"
          formControlName="trialEnd"
          id="date"
          presentation="date"
        ></ion-datetime>
      </ng-template>
    </ion-modal>
  </form>
</mpg-modal-layout>

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { Observable, switchMap } from 'rxjs';
import { UserService } from '../../auth/services';
import {
  VerificationCodeCreateRequest,
  VerificationCodeRequest,
} from '../models';
import { ModalService } from '../../shared/services';
import { take } from 'rxjs/operators';
import { VerificationCodeModalComponent } from '../components/verification-code-modal/verification-code-modal.component';

@Injectable({
  providedIn: 'root',
})
export class VerificationCodeService {
  static readonly BASE_URL = `${environment.PAYMENTS_SERVICE_API_URL}/v1/verification-codes`;

  constructor(
    private http: HttpClient,
    private userService: UserService,
    private modalService: ModalService,
  ) {}

  create(): Observable<void> {
    return this.userService.loggedUserId$.pipe(
      take(1),
      switchMap((userId) => {
        return this.http.post<void>(`${VerificationCodeService.BASE_URL}`, {
          userId,
        } as VerificationCodeCreateRequest);
      }),
    );
  }

  createVerificationCodeModal(): Observable<VerificationCodeRequest> {
    return this.create().pipe(
      switchMap(() => {
        return this.modalService.create<VerificationCodeRequest>(
          {
            component: VerificationCodeModalComponent,
            backdropDismiss: false,
          },
          'small',
        );
      }),
    );
  }
}

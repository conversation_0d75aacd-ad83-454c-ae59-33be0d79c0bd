import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { EntityListModalComponent } from './entity-list-modal.component';
import { IonicModule } from '@ionic/angular';
import { ModalLayoutModule } from '../modal-layout/modal-layout.module';
import { SelectAllDirectiveModule } from '../../directives/select-all/select-all-directive.module';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { RelativeDatePipeModule } from '../../pipes/relative-date/relative-date-pipe.module';
import { EntityListModule } from '../entity-list/entity-list.module';

@NgModule({
  imports: [
    CommonModule,
    IonicModule,
    ModalLayoutModule,
    SelectAllDirectiveModule,
    TranslateModule,
    ReactiveFormsModule,
    RelativeDatePipeModule,
    EntityListModule,
  ],
  declarations: [EntityListModalComponent],
  exports: [EntityListModalComponent],
})
export class EntityListModalModule {}

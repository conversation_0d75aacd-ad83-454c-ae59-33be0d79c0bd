<input
  #imageInput
  (change)="handleImageSelect($event)"
  accept="image/*"
  class="invisible"
  type="file"
/>
<ion-list class="ion-no-margin ion-no-padding">
  <ng-container *ngFor="let entity of entities; let i = index; let last = last">
    <ion-item *ngIf="loadingEntityId === entity.id && loadingProgress > 0">
      <ion-progress-bar
        *ngIf="loadingProgress < 100"
        [value]="loadingProgress / 100"
      ></ion-progress-bar>
    </ion-item>
    <ion-item-sliding
      #itemSliding
      *ngIf="loadingEntityId !== entity.id || !loadingProgress"
      [disabled]="!isEntityEditable(entity)"
    >
      <ion-item
        (click)="handleEntityClick(entity)"
        [button]="selectMode || entity[photoUrlProp]"
        [color]="selectMode && selectedEntity === entity ? 'dark' : undefined"
        [lines]="last ? 'none' : undefined"
      >
        <ion-chip
          *ngIf="chipPredicate && chipPredicate(entity)"
          [ngClass]="{ light: selectMode && selectedEntity === entity }"
          >{{ chipLabel | translate }}
        </ion-chip>
        <ion-thumbnail *ngIf="entity[photoUrlProp]" slot="start">
          <ion-img [src]="entity[photoUrlProp]"></ion-img>
        </ion-thumbnail>
        <ion-label class="ion-text-center">
          <ion-text>
            <pre>{{ entity[entityTextProp] }}</pre>
          </ion-text>
        </ion-label>
      </ion-item>
      <ion-item-options>
        <ion-item-option
          (click)="
            imageInput.click(); loadingEntityId = entity.id; itemSliding.close()
          "
          *ngIf="onNewPhoto"
          class="transparent"
        >
          <ion-icon color="dark" name="camera" slot="icon-only"></ion-icon>
        </ion-item-option>
        <ion-item-option
          (click)="handleEntityPopoverMenu($event, itemSliding, entity)"
          class="transparent"
        >
          <ion-icon
            color="dark"
            name="ellipsis-vertical"
            slot="icon-only"
          ></ion-icon>
        </ion-item-option>
      </ion-item-options>
    </ion-item-sliding>
  </ng-container>
</ion-list>

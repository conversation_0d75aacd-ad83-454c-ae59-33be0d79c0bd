$brand-success: var(--ion-color-primary);
$loader-size: 28px;
$check-height: calc($loader-size / 2);
$check-width: calc($check-height / 2);
$check-left: 3px;
$check-thickness: 4px;
$check-color: $brand-success;

.circle-loader {
  border: 4px solid rgba(0, 0, 0, 0.2);
  border-left-color: $check-color;
  animation: loader-spin 1.2s infinite linear;
  position: relative;
  display: inline-block;
  vertical-align: top;
  border-radius: 50%;
  width: $loader-size;
  height: $loader-size;

  &.secondary {
    border-left-color: var(--ion-color-secondary-shade);
  }
}

.load-complete {
  -webkit-animation: none;
  animation: none;
  border-color: rgba(0, 0, 0, 0.2) !important;
  transition: border 500ms ease-out;
}

.checkmark {
  display: none;

  &.visible {
    display: block;
  }

  &.draw:after {
    transform: scaleX(-1) rotate(135deg);
  }

  &.draw.animation:after {
    animation-duration: 800ms;
    animation-timing-function: ease;
    animation-name: checkmark;
    transform: scaleX(-1) rotate(135deg);
  }

  &:after {
    opacity: 1;
    height: $check-height;
    width: $check-width;
    transform-origin: left top;
    border-right: $check-thickness solid $check-color;
    border-top: $check-thickness solid $check-color;
    content: '';
    left: $check-left;
    top: 8px;
    position: absolute;
  }

  &.secondary:after {
    border-right: $check-thickness solid var(--ion-color-secondary-shade);
    border-top: $check-thickness solid var(--ion-color-secondary-shade);
  }
}

@keyframes loader-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes checkmark {
  0% {
    height: 0;
    width: 0;
    opacity: 1;
  }
  20% {
    height: 0;
    width: $check-width;
    opacity: 1;
  }
  40% {
    height: $check-height;
    width: $check-width;
    opacity: 1;
  }
  100% {
    height: $check-height;
    width: $check-width;
    opacity: 1;
  }
}

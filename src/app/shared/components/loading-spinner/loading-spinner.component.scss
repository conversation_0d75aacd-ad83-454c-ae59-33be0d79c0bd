.fullscreen-loader {
  z-index: 10000;
  position: fixed;
  background-color: rgba(0, 0, 0, 0.7);
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;

  img {
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 25vw;
    height: 25vw;
    min-width: 300px;
    min-height: 300px;
    border-radius: 50%;
    object-fit: cover;
  }
}

.block-loader {
  width: 100%;
  height: 100%;
  padding: 1rem;

  img {
    display: block;
    width: 25%;
    min-width: 100px;
    min-height: 100px;
    max-width: 100px;
    border-radius: 50%;
    object-fit: cover;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

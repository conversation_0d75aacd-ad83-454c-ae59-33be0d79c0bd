import { Component } from '@angular/core';
import { AnimationItem } from 'lottie-web';
import { AnimationOptions } from 'ngx-lottie';

@Component({
  selector: 'mpg-lottie-test',
  template: `
    <div class="lottie-container">
      <h2>PR Animation Test</h2>
      
      <!-- Lottie Animation -->
      <ng-lottie 
        [options]="options" 
        [width]="'300px'" 
        [height]="'400px'"
        (animationCreated)="onAnimationCreated($event)"
        (complete)="onComplete($event)"
        (loopComplete)="onLoopComplete($event)">
      </ng-lottie>
      
      <!-- Controls -->
      <div class="controls">
        <ion-button (click)="play()" fill="outline">
          <ion-icon name="play" slot="start"></ion-icon>
          Play
        </ion-button>
        
        <ion-button (click)="pause()" fill="outline">
          <ion-icon name="pause" slot="start"></ion-icon>
          Pause
        </ion-button>
        
        <ion-button (click)="stop()" fill="outline">
          <ion-icon name="stop" slot="start"></ion-icon>
          Stop
        </ion-button>
        
        <ion-button (click)="restart()" fill="outline">
          <ion-icon name="refresh" slot="start"></ion-icon>
          Restart
        </ion-button>
      </div>
      
      <!-- Animation Info -->
      <div class="info" *ngIf="animationItem">
        <p><strong>Duration:</strong> {{ getDuration() }}s</p>
        <p><strong>Frame Rate:</strong> {{ getFrameRate() }} fps</p>
        <p><strong>Total Frames:</strong> {{ getTotalFrames() }}</p>
        <p><strong>Current Frame:</strong> {{ getCurrentFrame() }}</p>
      </div>
    </div>
  `,
  styles: [`
    .lottie-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px;
      gap: 16px;
    }
    
    .controls {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
      justify-content: center;
    }
    
    .info {
      background: var(--ion-color-light);
      padding: 16px;
      border-radius: 8px;
      text-align: center;
      min-width: 200px;
    }
    
    .info p {
      margin: 4px 0;
      font-size: 14px;
    }
    
    ng-lottie {
      border: 2px solid var(--ion-color-medium);
      border-radius: 8px;
      background: var(--ion-color-light);
    }
  `]
})
export class LottieTestComponent {
  animationItem: AnimationItem | null = null;
  
  options: AnimationOptions = {
    path: '/assets/animations/pr.json',
    loop: true,
    autoplay: true
  };

  onAnimationCreated(animationItem: AnimationItem): void {
    this.animationItem = animationItem;
    console.log('Lottie animation created:', animationItem);
  }

  onComplete(event: any): void {
    console.log('Animation completed:', event);
  }

  onLoopComplete(event: any): void {
    console.log('Animation loop completed:', event);
  }

  play(): void {
    if (this.animationItem) {
      this.animationItem.play();
    }
  }

  pause(): void {
    if (this.animationItem) {
      this.animationItem.pause();
    }
  }

  stop(): void {
    if (this.animationItem) {
      this.animationItem.stop();
    }
  }

  restart(): void {
    if (this.animationItem) {
      this.animationItem.goToAndPlay(0);
    }
  }

  getDuration(): number {
    return this.animationItem ? Math.round(this.animationItem.getDuration() * 100) / 100 : 0;
  }

  getFrameRate(): number {
    return this.animationItem ? this.animationItem.frameRate : 0;
  }

  getTotalFrames(): number {
    return this.animationItem ? this.animationItem.totalFrames : 0;
  }

  getCurrentFrame(): number {
    return this.animationItem ? Math.round(this.animationItem.currentFrame) : 0;
  }
}

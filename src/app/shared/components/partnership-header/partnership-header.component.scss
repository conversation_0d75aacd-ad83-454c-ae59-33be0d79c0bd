.partnership-header {
  color: white;
  padding: 24px 16px;
  margin-bottom: 16px;
  border-radius: 0 0 16px 16px;

  .partnership-content {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;

    .partnership-logo {
      flex-shrink: 0;
      width: 150px;
      height: 150px;
      background: white;
      border-radius: 12px;
      overflow: hidden;

      ion-img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .partnership-text {
      flex: 1;

      h2 {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
      }

      p {
        margin: 0;
        font-size: 14px;
        opacity: 0.9;
        line-height: 1.4;
      }
    }
  }

  .promo-code-section {
    .promo-code-card {
      background: rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(10px);
      border-radius: 12px;
      padding: 16px;
      border: 1px solid rgba(255, 255, 255, 0.2);

      .promo-code-content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
        margin-bottom: 12px;

        .promo-label {
          font-size: 14px;
          opacity: 0.9;
        }

        .promo-code {
          background: rgba(255, 255, 255, 0.2);
          padding: 8px 16px;
          border-radius: 8px;
          font-family: 'Courier New', monospace;
          font-weight: bold;
          font-size: 16px;
          letter-spacing: 1px;
        }
      }

      .promo-actions {
        display: flex;
        gap: 8px;
        justify-content: center;
        align-items: center;

        ion-button {
          --border-radius: 8px;
          height: 36px;
          font-size: 12px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .partnership-header {
    .partnership-content {
      flex-direction: column;
      text-align: center;

      .partnership-logo {
        align-self: center;
      }
    }

    .promo-code-section {
      .promo-code-card {
        .promo-actions {
          flex-direction: column;

          ion-button {
            width: 100%;
          }
        }
      }
    }
  }
}

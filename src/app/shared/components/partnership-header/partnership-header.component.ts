import { Component, Input } from '@angular/core';
import { PartnershipConfig } from './partnership-header.model';
import { ToastService } from '../../services';

@Component({
  selector: 'mpg-partnership-header',
  templateUrl: './partnership-header.component.html',
  styleUrls: ['./partnership-header.component.scss'],
})
export class PartnershipHeaderComponent {
  @Input() config!: PartnershipConfig;

  constructor(private toastService: ToastService) {}

  async copyPromoCode() {
    try {
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(this.config.promoCode);
        this.toastService.showInfoToast(
          'nutrition.product-promotions.code-copied',
        );
      } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = this.config.promoCode;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        this.toastService.showInfoToast(
          'nutrition.product-promotions.code-copied',
        );
      }
    } catch (error) {
      console.error('Failed to copy promo code:', error);
    }
  }

  visitStore() {
    window.open(this.config.storeUrl, '_blank');
  }
}

<ion-content>
  <pan-zoom [config]="panZoomConfig">
    <div style="position: relative">
      <img [src]="src" />
    </div>
  </pan-zoom>
</ion-content>
<ion-footer>
  <ion-toolbar>
    <div class="flex-centered">
      <ion-fab-button
        (click)="handleZoomIn()"
        class="ion-margin-start"
        size="small"
      >
        <ion-icon name="add"></ion-icon>
      </ion-fab-button>
      <ion-fab-button
        (click)="handleZoomOut()"
        class="ion-margin-start"
        size="small"
      >
        <ion-icon name="remove"></ion-icon>
      </ion-fab-button>
    </div>
  </ion-toolbar>
</ion-footer>

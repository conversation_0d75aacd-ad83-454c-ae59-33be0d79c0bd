import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PhotosModalComponent } from './photos-modal.component';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { SwiperDirectiveModule } from '../../directives/swiper/swiper-directive.module';
import { ModalLayoutModule } from '../modal-layout/modal-layout.module';
import { NgxPanZoomModule } from 'ngx-panzoom';

@NgModule({
  imports: [
    CommonModule,
    IonicModule,
    TranslateModule,
    SwiperDirectiveModule,
    ModalLayoutModule,
    NgxPanZoomModule,
  ],
  declarations: [PhotosModalComponent],
  exports: [PhotosModalComponent],
})
export class PhotosModalModule {}

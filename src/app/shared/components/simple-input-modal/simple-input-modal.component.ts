import { Component, Input, OnInit } from '@angular/core';
import { Button } from '../../models';
import {
  FormBuilder,
  FormGroup,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { Locale, LocalizationService, ModalService } from '../../services';
import { InputType } from './simple-input-modal.model';
import { PlatformService } from '../../services/platform.service';
import { Observable } from 'rxjs';
import { MPGValidators } from '../../validators/validators';

@Component({
  selector: 'mpg-simple-input-modal',
  templateUrl: './simple-input-modal.component.html',
  styleUrls: ['./simple-input-modal.component.scss'],
})
export class SimpleInputModalComponent implements OnInit {
  @Input() type: InputType = 'text';
  @Input() title = '';
  @Input() label = '';
  @Input() value?: any;
  @Input() options?: any[];
  @Input() optionValueProp?: string;
  @Input() optionLabelProp?: string;
  @Input() selectMultiple = true;
  @Input() isRequired = true;
  @Input() datetimePresentation: 'date' | 'time' | 'date-time' = 'date';
  @Input() invalidDates: string[] = [];

  formGroup: FormGroup;
  modalButton: Button = {
    label: 'buttons.submit',
    handler: () => this.handleSubmit(),
    disabled: () => this.formGroup.invalid,
  };
  mode: string;
  locale$: Observable<Locale>;
  localeHourCycle23$: Observable<string>;

  constructor(
    private fb: FormBuilder,
    private modalService: ModalService,
    private platformService: PlatformService,
    private localizationService: LocalizationService,
  ) {}

  ngOnInit() {
    this.formGroup = this.fb.group({
      input: [this.getValue(), this.getValidators()],
    });

    this.mode = this.platformService.mode;
    this.locale$ = this.localizationService.locale$;
    this.localeHourCycle23$ = this.localizationService.localeHourCycle23$;
  }

  handleSubmit() {
    if (this.formGroup.invalid) {
      return;
    }

    if (!this.isRequired && !this.formGroup.value.input) {
      this.modalService.closeTopModal(true);
      return;
    }

    this.modalService.closeTopModal(this.formGroup.value.input);
  }

  private getValue() {
    if (Array.isArray(this.value)) {
      return this.value.map((e) => e[this.optionValueProp]);
    }

    return this.value || null;
  }

  private getValidators(): ValidatorFn[] {
    const validators: ValidatorFn[] = [];

    if (this.isRequired) {
      validators.push(Validators.required);
    }

    if (this.type === 'datetime' && this.datetimePresentation === 'date') {
      validators.push(MPGValidators.validValue(this.invalidDates));
    }

    return validators;
  }
}

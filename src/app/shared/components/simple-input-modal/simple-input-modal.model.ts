export type InputType =
  | 'text'
  | 'number'
  | 'email'
  | 'textarea'
  | 'select'
  | 'datetime';

export interface SimpleInputModalOptions {
  type: InputType;
  title: string;
  label?: string;
  value?: any;
  options?: any[];
  optionValueProp?: string;
  optionLabelProp?: string;
  selectMultiple?: boolean;
  isRequired?: boolean;
  datetimePresentation?: 'date' | 'time' | 'date-time';
  invalidDates?: string[];
}

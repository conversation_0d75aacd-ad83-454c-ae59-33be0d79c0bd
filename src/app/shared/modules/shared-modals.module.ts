import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SimpleInputModalModule } from '../components/simple-input-modal/simple-input-modal.module';
import { ChartModalModule } from '../components/chart-modal/chart-modal.module';
import { PhotosModalModule } from '../components/photos-modal/photos-modal.module';
import { EntityListModalModule } from '../components/entity-list-modal/entity-list-modal.module';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    SimpleInputModalModule,
    ChartModalModule,
    PhotosModalModule,
    EntityListModalModule,
  ],
})
export class SharedModalsModule {}

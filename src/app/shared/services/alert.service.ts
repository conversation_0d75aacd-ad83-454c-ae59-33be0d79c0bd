import { Injectable } from '@angular/core';
import { Alert<PERSON>ontroller, AlertOptions, Platform } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root',
})
export class AlertService {
  constructor(
    private alertController: AlertController,
    private translateService: TranslateService,
    private platform: Platform,
  ) {}

  async create(opts: AlertOptions): Promise<HTMLIonAlertElement> {
    const el = await this.alertController.create({
      ...opts,
      mode: this.platform.is('android') ? 'md' : 'ios',
    });

    await el.present();

    return el;
  }

  createDeleteAlert(deleteHandler: () => void) {
    return this.createConfirmAlert('alerts.confirm.delete', deleteHandler);
  }

  createCancelAlert(cancelHandler: () => void) {
    return this.createConfirmAlert('alerts.confirm.cancel', cancelHandler);
  }

  async createErrorAlert(error: any) {
    await this.create({
      header: this.translateService.instant('alerts.error'),
      message: error?.message || error?.error?.message || error,
      buttons: ['OK'],
    });
  }

  async createConfirmAlert(
    header: string,
    confirmHandler: () => void,
    translateParams?: object,
  ) {
    await this.create({
      header: this.translateService.instant(header, translateParams),
      buttons: [
        {
          text: this.translateService.instant('alerts.confirm.yes'),
          handler: confirmHandler,
        },
        {
          text: this.translateService.instant('alerts.confirm.no'),
          role: 'cancel',
        },
      ],
    });
  }
}

import { Injectable } from '@angular/core';
import { DatePipe } from '@angular/common';
import { Locale, LocalizationService } from './localization.service';
import { DateTime } from 'luxon';

@Injectable({
  providedIn: 'root',
})
export class DateService {
  private static ISO_8601_REGEX =
    /^\d{4}-\d\d-\d\dT\d\d:\d\d:\d\d(\.\d+)?(([+-]\d\d:\d\d)|Z)?$/i;

  private locale: Locale;

  constructor(
    private datePipe: DatePipe,
    private localizationService: LocalizationService,
  ) {
    this.localizationService.locale$.subscribe((locale) => {
      this.locale = locale;
    });
  }

  getCurrentDateMinusMonths(months: number): string {
    return this.getSimpleDate(
      DateTime.fromISO(this.getCurrentDate())
        .minus({ month: months })
        .toJSDate()
        .toISOString(),
    );
  }

  getDateMinusDays(date: string, days: number): string {
    return this.getSimpleDate(
      DateTime.fromISO(date).minus({ days }).toJSDate().toISOString(),
    );
  }

  getDatePlusDays(date: string, days: number): string {
    return this.getSimpleDate(
      DateTime.fromISO(date).plus({ days }).toJSDate().toISOString(),
    );
  }

  getDatePlusMonths(date: string, months: number): string {
    return this.getSimpleDate(
      DateTime.fromISO(date).plus({ months }).toJSDate().toISOString(),
    );
  }

  getDate(date?: string): string {
    if (!date) {
      return null;
    }

    return this.datePipe.transform(date, 'yyyy-MM-ddTHH:mm:ss');
  }

  getTime(date?: string): string {
    if (!date) {
      return null;
    }

    return this.datePipe.transform(date, 'HH:mm:ss');
  }

  trimTime(time: string): string {
    return `${time.substring(0, 5)}:00`;
  }

  getDateTime(date: string, time: string): string {
    return this.datePipe.transform(
      date,
      `yyyy-MM-ddT${time.substring(0, 5)}:00`,
    );
  }

  getZonedDateTime(date: string, time: string): string {
    const dateTime = this.getDateTime(date, time);

    return DateTime.fromISO(dateTime).setZone('local').toString();
  }

  sanitizeDatesInObject(object: any): any {
    if (object === null || object === undefined) {
      return;
    }

    if (typeof object !== 'object') {
      return;
    }

    for (const key of Object.keys(object)) {
      const value = object[key];
      if (
        value instanceof Date ||
        (typeof value === 'string' && DateService.ISO_8601_REGEX.test(value))
      ) {
        object[key] = this.getDate(value as string);
      } else if (typeof value === 'object') {
        this.sanitizeDatesInObject(value);
      }
    }

    return object;
  }

  getSimpleDate(date: string): string {
    return this.datePipe.transform(date, 'yyyy-MM-dd');
  }

  getCurrentDate(): string {
    return this.getSimpleDate(new Date().toISOString());
  }

  getCurrentTime(): string {
    return this.getTime(new Date().toISOString());
  }

  getCurrentDateTime(): string {
    return this.getCurrentDate() + 'T' + this.getCurrentTime();
  }

  isCurrentDate(date: string) {
    return this.getCurrentDate() === date;
  }

  getLocalizedMediumDate(date: string): string {
    return this.datePipe.transform(date, 'mediumDate', undefined, this.locale);
  }

  getLocalizedShortTime(time: string, locale?: string): string {
    const dummyDate = new Date();
    dummyDate.setHours(0, 0, 0); // Set the time portion to a fixed value
    const timeValue = new Date(dummyDate.getTime() + this.parseTime(time));
    return this.datePipe.transform(
      timeValue,
      'shortTime',
      undefined,
      locale || this.locale,
    );
  }

  getDaysDiff(date1: string, date2: string): number {
    return DateTime.fromISO(date1).diff(DateTime.fromISO(date2), 'days').days;
  }

  private parseTime(time: string): number {
    const [hours, minutes, seconds] = time.split(':').map(Number);
    return hours * 3600000 + minutes * 60000 + seconds * 1000;
  }
}

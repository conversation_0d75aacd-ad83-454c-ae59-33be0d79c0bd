import { Injectable } from '@angular/core';
import { Mo<PERSON><PERSON>ontroller, ModalOptions } from '@ionic/angular';
import { filter, from, Observable, switchMap } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import {
  InputType,
  SimpleInputModalOptions,
} from '../components/simple-input-modal/simple-input-modal.model';
import { SimpleInputModalComponent } from '../components/simple-input-modal/simple-input-modal.component';
import { Button } from '../models';
import { ChartModalOptions } from '../components/chart-modal/chart-modal.model';
import { ChartModalComponent } from '../components/chart-modal/chart-modal.component';
import { PhotosModalComponent } from '../components/photos-modal/photos-modal.component';
import { EntitySelectorModalComponent } from '../components/entity-selector-modal/entity-selector-modal.component';
import { EntitySelectorModalOptions } from '../components/entity-selector-modal/entity-selector-modal.model';
import { EntityListModalOptions } from '../components/entity-list-modal/entity-list-modal.model';
import { EntityListModalComponent } from '../components/entity-list-modal/entity-list-modal.component';

export type ModalSize =
  | 'input-number'
  | 'input-text'
  | 'extra-small'
  | 'small'
  | 'below-medium'
  | 'medium'
  | 'large'
  | 'xl'
  | 'fullscreen';

@Injectable({
  providedIn: 'root',
})
export class ModalService {
  private static INPUT_TYPE_MODAL_SIZE_MAP: { [key in InputType]: ModalSize } =
    {
      text: 'input-text',
      number: 'input-number',
      email: 'input-text',
      textarea: 'extra-small',
      select: 'input-text',
      datetime: 'input-text',
    };

  private hiddenModals: HTMLIonModalElement[] = [];

  constructor(private modalController: ModalController) {}

  get closeButton(): Button {
    return {
      label: 'buttons.close',
      handler: () => {
        this.closeTopModal();
      },
    };
  }

  async closeTopModal(data?: any) {
    const modal = await this.modalController.getTop();

    await modal.dismiss(data);
  }

  async closeAllModals() {
    let topModal = await this.modalController.getTop();
    while (topModal) {
      topModal.classList.add('invisible');
      await topModal.dismiss();
      topModal = await this.modalController.getTop();
    }
  }

  async hideTopModal() {
    const modal = await this.modalController.getTop();
    if (!modal) {
      return;
    }

    if (modal.classList.contains('mpg-modal-fullscreen')) {
      return;
    }

    modal.classList.add('invisible');

    this.hiddenModals.push(modal);
  }

  create<T = any>(opts: ModalOptions, size: ModalSize): Observable<T> {
    return from(
      this.modalController.create({
        ...opts,
        cssClass: this.getModalClass(size),
      }),
    ).pipe(
      tap(() => {
        this.hideTopModal();
      }),
      switchMap((el) => from(el.present()).pipe(map(() => el))),
      switchMap((el) => {
        return from(el.onDidDismiss<T>()).pipe(
          tap(() => {
            this.showLastHiddenModal();
          }),
          filter((ev) => !!ev.data),
          map((ev) => ev.data),
        );
      }),
    );
  }

  createSimpleInput(
    opts: SimpleInputModalOptions,
  ): Observable<string | string[]> {
    return this.create<string>(
      {
        component: SimpleInputModalComponent,
        componentProps: opts,
      },
      ModalService.INPUT_TYPE_MODAL_SIZE_MAP[opts.type],
    );
  }

  createChart(opts: ChartModalOptions) {
    this.create(
      {
        component: ChartModalComponent,
        componentProps: opts,
      },
      'medium',
    ).subscribe();
  }

  createEntitySelector<T = any>(opts: EntitySelectorModalOptions) {
    return this.create<T>(
      {
        component: EntitySelectorModalComponent,
        componentProps: {
          ...opts,
        },
      },
      opts.size,
    );
  }

  showPhoto(src: string, size: ModalSize = 'xl') {
    this.create(
      {
        component: PhotosModalComponent,
        componentProps: {
          src,
        },
      },
      size,
    ).subscribe();
  }

  createEntityListModal<T = any>(opts: EntityListModalOptions): Observable<T> {
    return this.create<T>(
      {
        component: EntityListModalComponent,
        componentProps: {
          ...opts,
        },
      },
      'small',
    );
  }

  showEntityListModal(opts: EntityListModalOptions) {
    this.createEntityListModal(opts).subscribe();
  }

  showLastHiddenModal() {
    const lastHiddenModal = this.hiddenModals.pop();
    if (lastHiddenModal) {
      lastHiddenModal.classList.remove('invisible');
    }
  }

  private getModalClass(size: ModalSize) {
    return ['mpg-modal', `mpg-modal-${size}`];
  }
}

import { Component, OnInit } from '@angular/core';
import { ModalService } from '../../../shared/services';
import { ProductPromotionFormModalComponent } from '../../../nutrition/components/product-promotion-form-modal/product-promotion-form-modal.component';
import { ProductPromotionService } from '../../../nutrition/services';
import { ViewDidEnter } from '@ionic/angular';
import { ProductPromotionStore } from '../../../nutrition/models';
import { switchMap, tap } from 'rxjs/operators';
import { BehaviorSubject } from 'rxjs';
import { ProductPromotionBulkFormModalComponent } from '../../../nutrition/components/product-promotion-bulk-form-modal/product-promotion-bulk-form-modal.component';


@Component({
  selector: 'mpg-product-promotions',
  templateUrl: './product-promotions.page.html',
  styleUrls: ['./product-promotions.page.scss'],
})
export class ProductPromotionsPage implements OnInit, ViewDidEnter {
  stores: ProductPromotionStore[];
  isLoading = false;
  selectedSegment = 'supermarkets';
  private readonly searchSubject$ = new BehaviorSubject<string>(null);

  constructor(
    private modalService: ModalService,
    private productPromotionService: ProductPromotionService,
  ) {}

  handleRefresh(event: any) {
    this.ionViewDidEnter();
    event.target.complete();
  }

  ngOnInit() {}

  ionViewDidEnter() {
    this.searchSubject$
      .pipe(
        tap(() => {
          this.isLoading = true;
        }),
        switchMap((search) => {
          return this.productPromotionService.getAll(search);
        }),
      )
      .subscribe((stores) => {
        this.stores = stores;
        this.isLoading = false;
      });
  }

  handleAddPromotion() {
    this.modalService
      .create(
        {
          component: ProductPromotionFormModalComponent,
        },
        'xl',
      )
      .subscribe(() => {
        this.handleSearchClear();
      });
  }

  handleAddPromotions() {
    this.modalService
      .create(
        {
          component: ProductPromotionBulkFormModalComponent,
        },
        'xl',
      )
      .subscribe(() => {
        this.handleSearchClear();
      });
  }

  handleSearch(event: any) {
    this.searchSubject$.next(event.target.value || null);
  }

  handleSearchClear() {
    this.searchSubject$.next(null);
  }

  handleSegmentChange(event: any) {
    this.selectedSegment = event.detail.value;
  }
}

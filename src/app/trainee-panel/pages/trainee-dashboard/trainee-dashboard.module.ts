import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';
import { TraineeDashboardPageRoutingModule } from './trainee-dashboard-routing.module';
import { TraineeDashboardPage } from './trainee-dashboard.page';
import { PieChartModule } from '@swimlane/ngx-charts';
import { WeightModalModule } from '../../../training/components/weight-modal/weight-modal.module';
import { WeeklyBodyWeightRecordGridModule } from '../../../training/components/weekly-body-weight-record-grid/weekly-body-weight-record-grid.module';
import { TranslateModule } from '@ngx-translate/core';
import { CardComponentModule } from '../../../shared/components/card/card.module';
import { DayOfWeekPipeModule } from '../../../shared/pipes/day-of-week/day-of-week-pipe.module';
import { TimeFormatPipeModule } from '../../../shared/pipes/time-format/time-format-pipe.module';
import { SleepQualityTimeFormModule } from '../../../training/components/sleep-quality-time-form/sleep-quality-time-form.module';
import { WhoopChartModule } from '../../../training/components/whoop-chart/whoop-chart.module';
import { WhoopDashboardModule } from '../../../training/components/whoop-dashboard/whoop-dashboard.module';
import { LoadingSpinnerComponentModule } from '../../../shared/components/loading-spinner/loading-spinner.module';
import { HasSubscriptionPlanDirectiveModule } from '../../../payments/directives/has-subscription-plan-directive.module';
import { LottieModule } from 'ngx-lottie';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TraineeDashboardPageRoutingModule,
    PieChartModule,
    WeightModalModule,
    WeeklyBodyWeightRecordGridModule,
    TranslateModule,
    CardComponentModule,
    DayOfWeekPipeModule,
    TimeFormatPipeModule,
    ReactiveFormsModule,
    SleepQualityTimeFormModule,
    WhoopChartModule,
    WhoopDashboardModule,
    LoadingSpinnerComponentModule,
    HasSubscriptionPlanDirectiveModule,
    LottieModule,
  ],
  declarations: [TraineeDashboardPage],
})
export class TraineeDashboardModule {}

<ion-header>
  <ion-toolbar color="dark">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>{{ 'dashboard' | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content *ngIf="isLoading">
  <mpg-loading-spinner [isLoading]="true"></mpg-loading-spinner>
</ion-content>
<ion-content *ngIf="!isLoading">
  <ion-refresher (ionRefresh)="handleRefresh($event)" slot="fixed">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  <ion-row>
    <ion-col *ngIf="activeWorkoutRecord" offset-lg="3" size="12" size-lg="6">
      <mpg-card
        [button]="getWorkoutButton()"
        [popoverMenuButtons]="getWorkoutPopoverMenuButtons()"
        title="training.active-workout"
      >
        <ion-row
          *ngFor="let exercise of workoutExercises; let last = last; let index = index"
          [ngClass]="{'border-bottom': !last &&
        !(
          workoutExercises[index + 1] &&
          workoutExercises[index + 1].mainOrderNumber ===
            exercise.mainOrderNumber
        )}"
        >
          <ion-col class="border-right" size="2">
            <ion-text color="dark"
              ><h2>{{ getOrderNumber(exercise) }}</h2></ion-text
            >
          </ion-col>
          <ion-col size="10">
            <ion-text color="dark"
              ><h2>
                {{ 'exercises.' + exercise.exercise.id | translate : { fallback:
                exercise.exercise.name } }}
              </h2>
            </ion-text>
          </ion-col>
        </ion-row>
      </mpg-card>
    </ion-col>
    <ion-col offset-lg="3" size="12" size-lg="6">
      <mpg-card class="weight-container" title="weight.today-s-weight">
        <ion-row class="p-relative ion-margin-top">
          <ion-img src="/assets/icon/weight.svg"></ion-img>
          <ion-card-title class="today-weight">
            <h1
              (click)="handleSetWeight()"
              *ngIf="currentWeight"
              class="today-weight"
            >
              {{ currentWeight }} {{ 'weight.kg' | translate }}
            </h1>
            <ion-fab-button
              (click)="handleSetWeight()"
              *ngIf="!currentWeight"
              color="primary"
            >
              <ion-icon src="assets/icon/feet.svg"></ion-icon>
            </ion-fab-button>
          </ion-card-title>
        </ion-row>
        <ion-row class="ion-margin-bottom">
          <ion-col size="12">
            <mpg-weekly-body-weight-record-grid
              [weeklyBodyWeightRecord]="weeklyBodyWeightRecord"
            >
            </mpg-weekly-body-weight-record-grid>
          </ion-col>
        </ion-row>
      </mpg-card>
    </ion-col>
    <ion-col offset-lg="3" size="12" size-lg="6">
      <mpg-card
        *ngIf="!sleepQualityRecord?.whoopRecord"
        [popoverMenuButtons]="sleepQualityButtons"
        title="sleep-quality.label"
      >
        <div class="sleep-quality-container flex-space-around">
          <ion-fab-button
            (click)="handleSleepQualityRecord(rating);"
            *ngFor="let rating of sleepQualityRatings"
            [class.disabled]="sleepQualityRecord && sleepQualityRecord.rating !== (rating * 20)"
            [class]="'rating-' + rating"
          >
            <ion-icon src="/assets/icon/rating-{{rating}}.svg"></ion-icon>
          </ion-fab-button>
        </div>
        <mpg-sleep-quality-time-form
          (onUpdate)="handleSleepQualityUpdate($event)"
          *ngIf="sleepQualityRecord"
          [sleepQualityRecord]="sleepQualityRecord"
        ></mpg-sleep-quality-time-form>
      </mpg-card>
      <ng-container *mpgHasSubscriptionPlan="SubscriptionPlanType.PREMIUM">
        <mpg-whoop-dashboard
          *ngIf="sleepQualityRecord?.whoopRecord"
          [sleepQualityRecord]="sleepQualityRecord"
        ></mpg-whoop-dashboard>
      </ng-container>
    </ion-col>

    <ion-col
      *ngIf="nextWorkout && !activeWorkoutRecord"
      offset-lg="3"
      size="12"
      size-lg="6"
    >
      <mpg-card
        [button]="getWorkoutButton()"
        [popoverMenuButtons]="getWorkoutPopoverMenuButtons()"
        [subtitle]="(nextWorkout.date | dayOfWeek | translate) +
        (nextWorkout.time ? ' - ' + (nextWorkout.time | timeFormat) : '')"
        title="training.next-workout"
      >
        <ion-row
          *ngFor="let exercise of workoutExercises; let last = last; let index = index"
          [ngClass]="{'border-bottom': !last &&
        !(
          workoutExercises[index + 1] &&
          workoutExercises[index + 1].mainOrderNumber ===
            exercise.mainOrderNumber
        )}"
        >
          <ion-col class="border-right" size="2">
            <ion-text color="dark"
              ><h2>{{ getOrderNumber(exercise) }}</h2></ion-text
            >
          </ion-col>
          <ion-col size="10">
            <ion-text color="dark"
              ><h2>
                {{ 'exercises.' + exercise.exercise.id | translate : { fallback:
                exercise.exercise.name } }}
              </h2>
            </ion-text>
          </ion-col>
        </ion-row>
      </mpg-card>
    </ion-col>
    <!--    <ion-col offset-lg="3" size="12" size-lg="6">-->
    <!--      <ion-card class="chart-container ion-text-center">-->
    <!--        <ion-card-header class="ion-no-padding">-->
    <!--          <ion-card-title>-->
    <!--            <h1>Macros for today</h1>-->
    <!--          </ion-card-title>-->
    <!--        </ion-card-header>-->
    <!--        <ion-card-content class="ion-no-padding">-->
    <!--          <ion-row>-->
    <!--            <ion-col size="6">-->
    <!--              <div class="nutrition-target">-->
    <!--                <div class="macros">-->
    <!--                  <h2 class="protein">Protein: {{nutritionTarget?.protein}}</h2>-->
    <!--                  <h2 class="carbs">Carbs: {{nutritionTarget?.carbs}}</h2>-->
    <!--                  <h2 class="fat">Fat: {{nutritionTarget?.fat}}</h2>-->
    <!--                  <ion-card-title>-->
    <!--                    <h1 class="calories ion-hide-md-down">-->
    <!--                      Calories: {{nutritionTarget?.calories}}-->
    <!--                    </h1>-->
    <!--                  </ion-card-title>-->
    <!--                </div>-->
    <!--              </div>-->
    <!--            </ion-col>-->
    <!--            <ion-col size="6">-->
    <!--              &lt;!&ndash;              <div #chartContainer>&ndash;&gt;-->
    <!--              &lt;!&ndash;                <ngx-charts-pie-chart&ndash;&gt;-->
    <!--              &lt;!&ndash;                  [results]="nutritionTargetChartData"&ndash;&gt;-->
    <!--              &lt;!&ndash;                  [animations]="true"&ndash;&gt;-->
    <!--              &lt;!&ndash;                  [trimLabels]="false"&ndash;&gt;-->
    <!--              &lt;!&ndash;                  [doughnut]="true"&ndash;&gt;-->
    <!--              &lt;!&ndash;                  [view]="[chartContainer.offsetWidth, (chartContainer.offsetWidth)]"&ndash;&gt;-->
    <!--              &lt;!&ndash;                  [customColors]="nutritionTargetChartColours"&ndash;&gt;-->
    <!--              &lt;!&ndash;                >&ndash;&gt;-->
    <!--              &lt;!&ndash;                </ngx-charts-pie-chart>&ndash;&gt;-->
    <!--              &lt;!&ndash;              </div>&ndash;&gt;-->
    <!--            </ion-col>-->
    <!--          </ion-row>-->
    <!--        </ion-card-content>-->
    <!--        <ion-card-title class="ion-hide-md-up">-->
    <!--          <h2 class="calories">Calories: {{nutritionTarget?.calories}}</h2>-->
    <!--        </ion-card-title>-->
    <!--      </ion-card>-->
    <!--    </ion-col>-->
  </ion-row>
</ion-content>

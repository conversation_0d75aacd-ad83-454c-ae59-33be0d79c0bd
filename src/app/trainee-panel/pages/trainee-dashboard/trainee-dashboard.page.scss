.weight-container {
  ion-img {
    margin: 0 auto 1rem;

    &::part(image) {
      width: 12rem;
      height: 12rem;
      filter: invert(28%) sepia(23%) saturate(4304%) hue-rotate(331deg) brightness(97%) contrast(89%) drop-shadow(3px 3px 2px rgba(0, 0, 0, 0.7));
    }
  }

  h1.today-weight {
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
  }

  ion-card-title.today-weight {
    position: absolute;
    bottom: 20%;
    left: 50%;
    transform: translate(-50%);

    ion-icon {
      font-size: 40px;
    }

    ion-fab-button {
      filter: drop-shadow(2px 2px 1px rgba(0, 0, 0, 0.7));
    }
  }
}

.sleep-quality-container {
  margin: 1rem;

  ion-icon {
    font-size: 50px;
  }

  .disabled {
    opacity: 0.5;
  }

  ion-fab-button {
    filter: drop-shadow(2px 2px 1px rgba(0, 0, 0, 0.3));
  }
}

.chart-container {
  margin-top: 0;
}

.nutrition-target {
  color: var(--ion-color-dark);
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;

  .macros {
    h2 {
      padding: 0.5rem 1rem;
      border-radius: 20px;
      width: 75%;
      margin: 0 auto 1rem;
      box-sizing: content-box;

      &.protein {
        background-color: #ffffff;
      }

      &.carbs {
        background-color: #00966e;
      }

      &.fat {
        background-color: #d62612;
      }
    }
  }
}

.calories {
  margin-bottom: 1rem;
}

.black-friday {
  font-family: 'Orbitron', sans-serif;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 250px;
  background: url("/assets/images/black_friday.jpg") no-repeat center/cover;
  color: white;
  flex-direction: column;
  margin: 10px;
  overflow: hidden;


  h2 {
    width: 80%;
    max-width: 500px;
  }

  .background {
    padding-bottom: 10px;
    padding-top: 10px;
    height: 100%;
    width: 100%;
    background: rgba(0, 0, 0, 0.7);

  }

  .title {
    font-size: 2rem;
    margin-top: 20px;
    text-align: center;
    padding: 1rem;
  }

  .cta {
    font-size: 25px;
    border-radius: 20px;
    border: 2px solid white;
    padding: 10px;
  }

  .clock {
    width: 80vw;
    max-width: 600px;
    height: 100px;
    display: flex;

    div {
      width: 25%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 30px;
      position: relative;

      &:first-of-type {
        border-left: none;
      }

      &:after {
        font-size: 12px;
        position: absolute;
        bottom: 20px;
      }

      &.days {
        &:after {
          content: "Days";
          right: 25px;
        }
      }

      &.hours {
        &:after {
          content: "Hours";
          right: 20px;
        }
      }

      &.minutes {
        &:after {
          content: "Minutes";
          right: 15px;
        }
      }

      &.seconds {
        &:after {
          content: "Seconds";
          right: 12px;
        }
      }
    }
  }
}


/*// Glow Border Animation //*/

.animated-border-box, .animated-border-box-glow {
  overflow: hidden;
  z-index: 0;
  /* Border Radius */
  border-radius: 20px;
}

.animated-border-box-glow {
  overflow: hidden;
  /* Glow Blur */
  filter: blur(20px);
}

.animated-border-box:before, .animated-border-box-glow:before {
  content: '';
  z-index: -2;
  text-align: center;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(0deg);
  position: absolute;
  width: 99999px;
  height: 99999px;
  background-repeat: no-repeat;
  background-position: 0 0;
  /*border color, change middle color*/
  background-image: conic-gradient(rgba(0, 0, 0, 0), #0b32e8, rgba(0, 0, 0, 0) 25%);
  /* change speed here */
  animation: rotate 4s linear infinite;
}

.animated-border-box:after {
  content: '';
  position: absolute;
  z-index: -1;
  /* border width */
  left: 5px;
  top: 5px;
  /* double the px from the border width left */
  width: calc(100% - 10px);
  height: calc(100% - 10px);
  /*bg color*/
  background: transparent;
  /*box border radius*/
  border-radius: 7px;
}

@keyframes rotate {
  100% {
    transform: translate(-50%, -50%) rotate(1turn);
  }
}

.border-radius {
  border-radius: 20px;
}



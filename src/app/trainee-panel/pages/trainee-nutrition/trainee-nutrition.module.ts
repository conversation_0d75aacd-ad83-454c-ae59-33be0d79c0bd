import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';
import { TraineeNutritionPageRoutingModule } from './trainee-nutrition-page-routing.module';
import { TraineeNutritionPage } from './trainee-nutrition.page';
import { TranslateModule } from '@ngx-translate/core';
import { RelativeDatePipeModule } from '../../../shared/pipes/relative-date/relative-date-pipe.module';
import { MealRecordItemModule } from '../../../nutrition/components/meal-record-item/meal-record-item.module';
import { NutritionInfoChipsModule } from '../../../nutrition/components/nutrition-info-chips/nutrition-info-chips.module';
import { TimeAgoPipeModule } from '../../../shared/pipes/time-ago/time-ago-pipe.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TraineeNutritionPageRoutingModule,
    TranslateModule,
    RelativeDatePipeModule,
    MealRecordItemModule,
    NutritionInfoChipsModule,
    TimeAgoPipeModule,
  ],
  declarations: [TraineeNutritionPage],
})
export class TraineeNutritionModule {}

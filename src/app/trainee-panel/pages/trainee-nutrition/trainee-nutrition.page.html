<ion-header>
  <ion-toolbar color="dark">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>{{ "nutrition.label" | translate }}</ion-title>
    <ion-buttons slot="primary">
      <ion-button (click)="handlePopoverMenu($event)" *ngIf="mealDiary">
        <ion-icon
          ios="ellipsis-horizontal"
          md="ellipsis-vertical"
          slot="icon-only"
        ></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>
<ion-toolbar color="dark">
  <div class="flex-space-between">
    <ion-fab-button (click)="handleDateBackClick()" color="dark" size="small">
      <ion-icon name="chevron-back"></ion-icon>
    </ion-fab-button>
    <ion-datetime-button class="date-button" datetime="datetime">
      <ion-text slot="date-target"
        >{{ (selectedDateControl?.value | relativeDate : (locale$ | async)) ||
        (selectedDateControl?.value | date :'mediumDate' : undefined : (locale$
        | async)) }}
      </ion-text>
    </ion-datetime-button>
    <ion-fab-button
      (click)="handleDateForwardClick()"
      color="dark"
      size="small"
    >
      <ion-icon name="chevron-forward"></ion-icon>
    </ion-fab-button>
  </div>

  <ion-modal [keepContentsMounted]="true">
    <ng-template>
      <ion-datetime
        (ionChange)="handleDateChange($event)"
        [value]="selectedDateControl?.value"
        id="datetime"
        presentation="date"
      ></ion-datetime>
    </ng-template>
  </ion-modal>
</ion-toolbar>
<ion-toolbar *ngIf="latestMealRecord && !mealDiary" color="dark">
  <ion-item
    (click)="selectedDateControl.setValue(latestMealRecord.date)"
    [button]="true"
    color="dark"
  >
    <ion-label class="ion-text-center">
      <ion-text
        >{{ 'nutrition.latest-meal' | translate }} ({{ latestMealRecordDateTime
        | timeAgo : (locale$ | async) | lowercase }})
      </ion-text>
    </ion-label>
  </ion-item>
</ion-toolbar>
<ion-toolbar *ngIf="mealDiary" color="dark">
  <div class="w-full flex-centered">
    <mpg-nutrition-info-chips
      [nutritionInfo]="mealDiary?.nutritionInfo"
      color="light"
    ></mpg-nutrition-info-chips>
  </div>
</ion-toolbar>
<ion-fab #fab horizontal="end" slot="fixed" vertical="bottom">
  <ion-fab-button>
    <ion-icon name="add"></ion-icon>
  </ion-fab-button>
  <ion-fab-list side="top">
    <ion-fab-button
      (click)="handleCreateMeal()"
      [attr.data-desc]="'nutrition.create-meal' | translate"
    >
      <ion-icon name="time"></ion-icon>
    </ion-fab-button>
    <ion-fab-button
      (click)="handleQuickAdd()"
      [attr.data-desc]="'nutrition.quick-add' | translate"
    >
      <ion-icon name="flash-sharp"></ion-icon>
    </ion-fab-button>
    <ion-fab-button
      (click)="handleAIMealRecognition()"
      [attr.data-desc]="'nutrition.ai-meal-recognition' | translate"
    >
      <ion-icon name="camera"></ion-icon>
    </ion-fab-button>
    <ion-fab-button
      (click)="handleExploreIdeas()"
      [attr.data-desc]="'nutrition.explore-ideas' | translate"
    >
      <ion-icon name="bulb"></ion-icon>
    </ion-fab-button>
  </ion-fab-list>
</ion-fab>
<ion-content>
  <ion-refresher (ionRefresh)="handleRefresh($event)" slot="fixed">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  <ion-row>
    <ion-col class="ion-no-padding" offset-lg="3" size-lg="6">
      <div class="container">
        <mpg-meal-record-item
          (onAddFoodClick)="handleLogFood(mealRecord)"
          (onDateChange)="selectedDateControl.setValue($event)"
          (onUpdate)="handleMealRecordUpdate($event)"
          *ngFor="let mealRecord of mealDiary?.mealRecords"
          [date]="selectedDateControl.value"
          [mealRecord]="mealRecord"
          [mealRecords]="mealDiary.mealRecords"
        ></mpg-meal-record-item>
      </div>
    </ion-col>
  </ion-row>
</ion-content>

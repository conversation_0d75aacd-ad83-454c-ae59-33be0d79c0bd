import { Component, OnInit } from '@angular/core';
import { ViewDidEnter } from '@ionic/angular';
import {
  AlertService,
  DateService,
  Locale,
  LocalizationService,
  ModalService,
  PopoverService,
  ToastService,
} from '../../../shared/services';
import { FormControl } from '@angular/forms';
import { forkJoin, Observable, switchMap } from 'rxjs';
import {
  FoodRecord,
  LatestMealRecord,
  MealDiary,
  MealRecord,
} from '../../../nutrition/models';
import {
  FoodRecordService,
  MealDiaryService,
  MealRecordService,
} from '../../../nutrition/services';
import { ActivatedRoute, Router } from '@angular/router';
import { map } from 'rxjs/operators';
import { FoodSearchModalComponent } from '../../../nutrition/components/food-search-modal/food-search-modal.component';
import { QuickAddModalComponent } from '../../../nutrition/components/quick-add-modal/quick-add-modal.component';
import { SubscriptionPlanType } from '../../../payments/enumerations';
import { SubscriptionPlanService } from '../../../payments/services';
import { MealIdeasExploreModalComponent } from '../../../nutrition/components/meal-ideas-explore-modal/meal-ideas-explore-modal.component';
import { AIMealRecognitionModalComponent } from '../../../nutrition/components/ai-meal-recognition-modal/ai-meal-recognition-modal.component';

@Component({
  selector: 'mpg-trainee-nutrition',
  templateUrl: './trainee-nutrition.page.html',
  styleUrls: ['./trainee-nutrition.page.scss'],
})
export class TraineeNutritionPage implements OnInit, ViewDidEnter {
  mealDiary: MealDiary;
  latestMealRecord: LatestMealRecord;
  selectedDateControl: FormControl<string>;
  locale$: Observable<Locale>;

  constructor(
    private dateService: DateService,
    private modalService: ModalService,
    private localizationService: LocalizationService,
    private mealDiaryService: MealDiaryService,
    private mealRecordService: MealRecordService,
    private foodRecordService: FoodRecordService,
    private popoverService: PopoverService,
    private alertService: AlertService,
    private toastService: ToastService,
    private subscriptionPlanService: SubscriptionPlanService,
    private router: Router,
    private route: ActivatedRoute,
  ) {}

  get latestMealRecordDateTime(): string {
    return this.dateService.getDateTime(
      this.latestMealRecord.date,
      this.latestMealRecord.time,
    );
  }

  handleRefresh(event: any) {
    this.ionViewDidEnter();
    event.target.complete();
  }

  ngOnInit() {
    this.selectedDateControl = new FormControl<string>(null);
    this.selectedDateControl.valueChanges
      .pipe(
        switchMap((date) => {
          this.router.navigate([], { queryParams: { date } });

          return forkJoin({
            mealDiary: this.mealDiaryService.getMealDiary(date),
            latestMealRecord: this.mealRecordService.getLatest(date),
          });
        }),
      )
      .subscribe(({ mealDiary, latestMealRecord }) => {
        this.mealDiary = mealDiary;
        this.latestMealRecord = latestMealRecord;
      });

    this.locale$ = this.localizationService.locale$;
  }

  ionViewDidEnter(): void {
    this.route.queryParamMap
      .pipe(map((queryParamMap) => queryParamMap.get('date')))
      .subscribe((date) => {
        this.selectedDateControl.setValue(
          date || this.dateService.getCurrentDate(),
        );
      });
  }

  handleDateBackClick() {
    this.selectedDateControl.setValue(
      this.dateService.getDateMinusDays(this.selectedDateControl.value, 1),
    );
  }

  handleDateForwardClick() {
    this.selectedDateControl.setValue(
      this.dateService.getDatePlusDays(this.selectedDateControl.value, 1),
    );
  }

  handleLogFood(mealRecord?: MealRecord) {
    this.modalService
      .create<MealDiary>(
        {
          component: FoodSearchModalComponent,
          componentProps: {
            date: this.selectedDateControl.value,
            mealRecords: this.mealDiary?.mealRecords,
            mealOrderNumber: mealRecord?.orderNumber,
            onChange: (mealDiary: MealDiary) => {
              this.mealDiary = mealDiary;
            },
            onMealRecordCreate: (newMealRecord: MealRecord) => {
              this.handleMealRecordCreated(newMealRecord);
            },
          },
        },
        'fullscreen',
      )
      .subscribe((mealDiary: MealDiary) => {
        this.mealDiary = mealDiary;
      });
  }

  handleCreateMeal() {
    this.mealDiaryService
      .createMealRecordCreationModal(
        this.mealDiary?.mealRecords?.length + 1 || 1,
        this.selectedDateControl.value,
      )
      .subscribe((mealRecord) => {
        this.handleMealRecordCreated(mealRecord);
      });
  }

  handleDateChange(event: any) {
    this.selectedDateControl.setValue(
      this.dateService.getSimpleDate(event.detail.value),
    );
  }

  handleMealRecordCreated(newMealRecord: MealRecord) {
    if (!this.mealDiary) {
      this.selectedDateControl.setValue(this.selectedDateControl.value);
      return;
    }

    this.mealDiary.mealRecords =
      this.mealDiary.mealRecords.concat(newMealRecord);
  }

  handleMealRecordUpdate(mealDiary: MealDiary) {
    this.mealDiary = mealDiary;
  }

  handlePopoverMenu(event: MouseEvent) {
    this.popoverService.handlePopoverMenu(event, [
      {
        label: 'nutrition.copy-meals',
        handler: () => {
          this.modalService
            .createSimpleInput({
              title: 'nutrition.copy-meals',
              label: 'date',
              value: this.dateService.getCurrentDate(),
              isRequired: true,
              type: 'datetime',
              datetimePresentation: 'date',
              invalidDates: [this.selectedDateControl.value],
            })
            .subscribe((copyToDate: string) => {
              this.alertService.createConfirmAlert(
                'nutrition.copy-meals-confirm',
                () => {
                  this.mealDiaryService
                    .copyMealRecords(this.mealDiary.id, copyToDate)
                    .subscribe(() => {
                      this.selectedDateControl.setValue(copyToDate);

                      this.toastService.showInfoToast(
                        'nutrition.copy-meals-success',
                      );
                    });
                },
                {
                  date: this.dateService.getLocalizedMediumDate(
                    this.selectedDateControl.value,
                  ),
                  copyToDate:
                    this.dateService.getLocalizedMediumDate(copyToDate),
                },
              );
            });
        },
        subscriptionPlan: SubscriptionPlanType.ADVANCED,
      },
    ]);
  }

  async handleQuickAdd() {
    if (
      await this.subscriptionPlanService.showPaidFeatureModalIfNeeded(
        SubscriptionPlanType.ADVANCED,
      )
    ) {
      return;
    }

    this.modalService
      .create<FoodRecord>(
        {
          component: QuickAddModalComponent,
          componentProps: {},
        },
        'medium',
      )
      .pipe(
        switchMap((foodRecord: FoodRecord) => {
          return this.foodRecordService.createFoodLoggerModal({
            date: this.selectedDateControl.value,
            mealRecords: this.mealDiary?.mealRecords,
            mealOrderNumber: this.mealDiary?.mealRecords?.length || undefined,
            onMealRecordCreate: (mealRecord: MealRecord) => {
              this.handleMealRecordCreated(mealRecord);
            },
            opts: { foodRecord },
          });
        }),
      )
      .subscribe((mealDiary) => {
        this.mealDiary = mealDiary;
      });

    // this.modalService
    //   .create<MealDiary>(
    //     {
    //       component: QuickAddModalComponent,
    //       componentProps: {
    //         date: this.selectedDateControl.value,
    //         mealRecords: this.mealDiary?.mealRecords,
    //         mealOrderNumber: mealRecord?.orderNumber,
    //         onChange: (mealDiary: MealDiary) => {
    //           this.mealDiary = mealDiary;
    //         },
    //         onMealRecordCreate: (newMealRecord: MealRecord) => {
    //           this.handleMealRecordCreated(newMealRecord);
    //         },
    //       },
    //     },
    //     'fullscreen',
    //   )
    //   .subscribe((mealDiary: MealDiary) => {
    //     this.mealDiary = mealDiary;
    //   });
  }

  async handleExploreIdeas() {
    if (
      await this.subscriptionPlanService.showPaidFeatureModalIfNeeded(
        SubscriptionPlanType.ADVANCED,
      )
    ) {
      return;
    }

    this.modalService
      .create(
        {
          component: MealIdeasExploreModalComponent,
          componentProps: {},
        },
        'fullscreen',
      )
      .subscribe();
  }

  async handleAIMealRecognition() {
    if (
      await this.subscriptionPlanService.showPaidFeatureModalIfNeeded(
        SubscriptionPlanType.ADVANCED,
      )
    ) {
      return;
    }

    this.modalService
      .create(
        {
          component: AIMealRecognitionModalComponent,
          componentProps: {
            date: this.selectedDateControl.value,
            time: this.dateService.getCurrentTime(),
          },
        },
        'small',
      )
      .subscribe((mealDiary) => {
        this.mealDiary = mealDiary;
        this.toastService.showInfoToast(
          'nutrition.ai-meal-recognition-success',
        );
      });
  }
}

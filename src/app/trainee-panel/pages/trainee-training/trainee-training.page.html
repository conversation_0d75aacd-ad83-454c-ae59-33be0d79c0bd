<ion-header>
  <ion-toolbar color="dark">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>{{ 'training.training' | translate }}</ion-title>
    <ion-buttons slot="primary">
      <ion-button (click)="handlePopoverMenu($event)">
        <ion-icon
          ios="ellipsis-horizontal"
          md="ellipsis-vertical"
          slot="icon-only"
        ></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
  <ion-toolbar color="dark">
    <ion-segment
      (ionChange)="handleSegmentChange($event)"
      [value]="selectedSegment"
    >
      <ion-segment-button value="split">
        <ion-label>{{ 'training.split' | translate }}</ion-label>
      </ion-segment-button>
      <ion-segment-button value="history">
        <ion-label>{{ 'history' | translate }}</ion-label>
      </ion-segment-button>
    </ion-segment>
  </ion-toolbar>
</ion-header>
<ion-content>
  <ion-refresher (ionRefresh)="handleRefresh($event)" slot="fixed">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  <ng-container *ngIf="selectedSegment === 'split'">
    <ion-row>
      <ion-col
        *ngFor="let workout of activeTrainingSplit?.workouts"
        offset-lg="3"
        size="12"
        size-lg="6"
      >
        <mpg-workout-card
          [button]="getWorkoutButton(workout)"
          [chipTitle]="workout.id === nextWorkout?.workout.id ? 'training.next' : undefined"
          [isEditable]="false"
          [title]="((workout.id === nextWorkout?.workout.id ? 'training.workout-short' : 'training.workout') | translate) + ' ' + workout.orderNumber"
          [workout]="workout"
        ></mpg-workout-card>
      </ion-col>
    </ion-row>
    <ion-row *ngIf="activeTrainingSplit?.workouts?.length === 0" class="h-full">
      <ion-col class="flex-column-centered" offset-lg="3" size="12" size-lg="6">
        <ion-card>
          <ion-card-content>
            <ion-text class="ion-text-center" color="dark">
              <p>{{ 'training.no-workouts' | translate }}</p>
            </ion-text>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </ng-container>
  <ng-container *ngIf="selectedSegment === 'history'">
    <ion-row>
      <ion-col
        *ngFor="let workoutRecord of workoutRecordHistory?.content"
        offset-lg="3"
        size="12"
        size-lg="6"
      >
        <mpg-workout-record-card
          (onPRMessageClear)="handlePRMessageClear()"
          (onWorkoutRecordDelete)="handleWorkoutRecordDelete($event)"
          [finished]="finishedRecordId === workoutRecord.id"
          [title]="('training.workout' | translate) + ' ' + workoutRecord.workout.orderNumber"
          [workoutRecord]="workoutRecord"
        >
        </mpg-workout-record-card>
      </ion-col>
    </ion-row>

    <ion-infinite-scroll
      (ionInfinite)="handleInfiniteScroll($event)"
      [disabled]="workoutRecordHistory?.last"
      threshold="100px"
    >
      <ion-infinite-scroll-content
        class="mpg-infinite-scroll-content"
        loadingSpinner="bubbles"
        loadingText="Loading..."
      >
      </ion-infinite-scroll-content>
    </ion-infinite-scroll>
  </ng-container>
</ion-content>

import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SubscriptionPlanType } from '../payments/enumerations';
import { activeWorkoutGuard, subscriptionPlanGuard } from '../core/guards';

const routes: Routes = [
  {
    path: '',
    loadChildren: () =>
      import('./pages/tabs/trainee-panel-tabs.module').then(
        (m) => m.TraineePanelTabsPageModule,
      ),
  },
  {
    path: 'active-workout',
    loadChildren: () =>
      import('./pages/active-workout/active-workout.module').then(
        (m) => m.ActiveWorkoutPageModule,
      ),
    canActivate: [activeWorkoutGuard],
  },
  {
    path: 'training-schedule',
    loadChildren: () =>
      import('./pages/training-schedule/training-schedule.module').then(
        (m) => m.TrainingSchedulePageModule,
      ),
  },
  {
    path: 'time-machine',
    loadChildren: () =>
      import('./pages/time-machine/time-machine.module').then(
        (m) => m.TimeMachineModule,
      ),
    canActivate: [() => subscriptionPlanGuard(SubscriptionPlanType.ADVANCED)],
  },
  {
    path: 'visual-progress',
    loadChildren: () =>
      import('./pages/visual-progress-tabs/visual-progress-tabs.module').then(
        (m) => m.VisualProgressTabsPageModule,
      ),
  },
  {
    path: 'creatine-tracker',
    loadChildren: () =>
      import('./pages/creatine-tracker/creatine-tracker.module').then(
        (m) => m.CreatineTrackerModule,
      ),
    canActivate: [() => subscriptionPlanGuard(SubscriptionPlanType.ADVANCED)],
  },
  {
    path: 'subscription',
    loadChildren: () =>
      import('./pages/subscription/subscription.module').then(
        (m) => m.SubscriptionPageModule,
      ),
  },
  {
    path: 'integrations',
    loadChildren: () =>
      import('./pages/integrations/integrations.module').then(
        (m) => m.IntegrationsPageModule,
      ),
  },
  {
    path: 'legal',
    loadChildren: () =>
      import('./pages/legal/legal.module').then((m) => m.LegalPageModule),
  },
  {
    path: 'product-promotions',
    loadChildren: () =>
      import('./pages/product-promotions/product-promotions.module').then(
        (m) => m.ProductPromotionsPageModule,
      ),
  },
  {
    path: 'courses',
    loadChildren: () =>
      import('../training/pages/courses/courses.module').then(
        (m) => m.CoursesPageModule,
      ),
    data: {
      isTraineePanel: true,
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class TraineePanelRoutingModule {
  static readonly DEFAULT_URL = '/trainee/tabs/dashboard';
}

<ion-header>
  <ion-toolbar color="dark">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title> {{ 'invitations.label' | translate }}</ion-title>
  </ion-toolbar>
</ion-header>
<ion-fab #fab class="mb" horizontal="end" slot="fixed" vertical="bottom">
  <ion-fab-button>
    <ion-icon name="add"></ion-icon>
  </ion-fab-button>
  <ion-fab-list side="top">
    <ion-fab-button
      (click)="handleCreateInvitation()"
      [attr.data-desc]="'invitations.create' | translate"
    >
      <ion-icon name="mail"></ion-icon>
    </ion-fab-button>
  </ion-fab-list>
</ion-fab>
<ion-content (click)="fab.close()">
  <ion-refresher (ionRefresh)="handleRefresh($event)" slot="fixed">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  <ion-row>
    <ion-col class="ion-no-padding" offset-lg="3" size="12" size-lg="6">
      <ion-list class="ion-no-padding">
        <ion-item
          (click)="handleInvitationClick(invitation)"
          *ngFor="let invitation of invitations"
          [button]="true"
        >
          <ion-label>{{ invitation.instagram }}</ion-label>
          <ion-chip class="dark">
            <ion-label>{{ invitation.email }}</ion-label>
          </ion-chip>
        </ion-item>
      </ion-list>
    </ion-col>
  </ion-row>
</ion-content>

<ion-header>
  <ion-toolbar color="dark">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title> {{ 'notifications.label' | translate }}</ion-title>
    <ion-buttons *ngIf="false" slot="primary">
      <ion-button (click)="handlePopoverMenu($event)">
        <ion-icon
          ios="ellipsis-horizontal"
          md="ellipsis-vertical"
          slot="icon-only"
        ></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
  <ion-toolbar color="dark">
    <ion-row>
      <ion-col class="ion-no-padding" offset-lg="3" size-lg="6">
        <ion-searchbar
          (ionClear)="handleSearchClear()"
          (ionFocus)="handleSearchClick()"
          [placeholder]="'search' | translate"
          [value]="selectedTraineeDisplayValue"
        ></ion-searchbar>
      </ion-col>
    </ion-row>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-refresher (ionRefresh)="handleRefresh($event)" slot="fixed">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  <ion-row class="margin-top-lg">
    <ion-col
      *ngFor="let notification of notificationsPage?.content"
      class="ion-no-padding"
      offset-lg="3"
      size="12"
      size-lg="6"
    >
      <ion-item-sliding #itemSliding>
        <ion-item
          (click)="handleNotification(notification)"
          [button]="true"
          detail="false"
        >
          <div
            *ngIf="!notification.isRead"
            class="unread-indicator-wrapper"
            slot="start"
          >
            <div class="unread-indicator"></div>
          </div>
          <div class="ion-text-center notification-container w-full">
            <strong>{{ notification.title }}</strong>
            <br />
            <ion-text>{{ notification.body }}</ion-text>
          </div>
          <div class="metadata-end-wrapper" slot="end">
            <ion-note color="dark"
              >{{ notification.createdOn | timeAgo : (locale$ | async) }}
            </ion-note>
            <ion-icon color="dark" name="chevron-forward"></ion-icon>
          </div>
        </ion-item>
        <ion-item-options
          *ngIf="notification.type !== NotificationType.ADMIN && notification.type !== NotificationType.DIRECT && notification.data?.traineeId"
        >
          <ion-item-option
            (click)="
              handleSendNotification(
                itemSliding,
                notification
              )
            "
            color="dark"
          >
            <ion-icon
              color="light"
              name="notifications"
              slot="icon-only"
            ></ion-icon>
          </ion-item-option>
          <ion-item-option
            (click)="
              handleSelectTrainee(
                itemSliding,
                notification
              )
            "
            color="light"
          >
            <ion-icon
              color="dark"
              name="checkmark-circle"
              slot="icon-only"
            ></ion-icon>
          </ion-item-option>
          <ion-item-option
            (click)="
              handleTraineeDetails(
                itemSliding,
                notification
              )
            "
            color="secondary"
          >
            <ion-icon color="light" name="eye" slot="icon-only"></ion-icon>
          </ion-item-option>
          <ion-item-option
            (click)="
              handleImpersonateTrainee(
                itemSliding,
                notification
              )
            "
            color="primary"
          >
            <ion-icon
              color="light"
              name="log-in-outline"
              slot="icon-only"
            ></ion-icon>
          </ion-item-option>
        </ion-item-options>
      </ion-item-sliding>
    </ion-col>
  </ion-row>
  <ion-infinite-scroll
    (ionInfinite)="handleInfiniteScroll($event)"
    [disabled]="notificationsPage?.last"
    threshold="100px"
  >
    <ion-infinite-scroll-content
      class="mpg-infinite-scroll-content"
      loadingSpinner="bubbles"
      loadingText="Loading..."
    >
    </ion-infinite-scroll-content>
  </ion-infinite-scroll>
</ion-content>

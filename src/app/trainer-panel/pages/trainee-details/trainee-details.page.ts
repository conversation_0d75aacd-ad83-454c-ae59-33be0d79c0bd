import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { catchError, map } from 'rxjs/operators';

import { EMPTY, Observable } from 'rxjs';
import { ViewDidEnter } from '@ionic/angular';
import { CreateFitnessStateModalComponent } from '../../../training/components/create-fitness-state-modal/create-fitness-state-modal.component';
import { FitnessStateModalComponent } from '../../../training/components/fitness-state-modal/fitness-state-modal.component';

import { TraineeService } from '../../../training/services';
import {
  ModalService,
  PopoverService,
  ToastService,
} from '../../../shared/services';
import { AccountData, FitnessState, Trainee } from '../../../training/models';
import {
  SubscriptionPlanService,
  SubscriptionService,
} from '../../../payments/services';

@Component({
  selector: 'mpg-trainee-details',
  templateUrl: './trainee-details.page.html',
  styleUrls: ['./trainee-details.page.scss'],
})
export class TraineeDetailsPage implements ViewDidEnter {
  trainee: Trainee;
  fitnessState: FitnessState;
  isFitnessStateSelected = false;
  accountData: AccountData;

  constructor(
    private route: ActivatedRoute,
    private toastService: ToastService,
    private traineeService: TraineeService,
    private popoverService: PopoverService,
    private modalService: ModalService,
    private subscriptionService: SubscriptionService,
    private subscriptionPlanService: SubscriptionPlanService,
  ) {}

  ionViewDidEnter() {
    this.route.paramMap
      .pipe(map((paramMap) => paramMap.get('id')))
      .subscribe((traineeId) => {
        this.fetchTraineeInfo(traineeId);
      });
  }

  // async handlePopoverMenu(ev) {
  //   await this.popoverService.handlePopoverMenu(ev, [
  //     {
  //       label: 'Change picture',
  //       handler: async () => {
  //         const { base64String, blob } =
  //           await this.fileService.getPictureFromFileSystem();
  //
  //         const formData = new FormData();
  //         formData.append('picture', blob);
  //
  //         this.traineeService
  //           .changePicture(this.trainee.id, formData)
  //           .subscribe(() => {
  //             this.trainee.pictureUrl = base64String;
  //           });
  //       },
  //     },
  //     {
  //       label: 'Upload video',
  //       handler: async () => {
  //         await this.fileService.getVideoFromFileSystem();
  //       },
  //     },
  //   ]);
  // }

  async handleCreateFitnessState() {
    this.createCreateFitnessStateModal().subscribe((fitnessState) => {
      this.setFitnessState(fitnessState);
    });
  }

  handleEditFitnessState() {
    this.modalService.closeTopModal();

    this.createCreateFitnessStateModal(this.fitnessState).subscribe(
      (fitnessState) => {
        this.setFitnessState(fitnessState);
      },
    );
  }

  handleShowFitnessState() {
    this.modalService
      .create(
        {
          component: FitnessStateModalComponent,
          componentProps: {
            fitnessState: this.fitnessState,
            trainee: this.trainee,
            editHandler: () => this.handleEditFitnessState(),
          },
          canDismiss: true,
        },
        'xl',
      )
      .subscribe();
  }

  handlePopoverMenu(event: MouseEvent) {
    this.popoverService.handlePopoverMenu(event, [
      {
        label: 'auth.activate-account',
        handler: () => {
          this.traineeService.activateAccount(this.trainee.id).subscribe(() => {
            this.toastService.showInfoToast('auth.activate-account-success');
            this.accountData.isEnabled = true;
            this.accountData.isEmailVerified = true;
          });
        },
        disabled: () => this.accountData?.isEnabled,
      },
      {
        label: 'auth.promote-to-pro',
        handler: () => {
          this.traineeService.promoteToPro(this.trainee.id).subscribe(() => {
            this.toastService.showInfoToast('auth.promote-to-pro-success');
            this.accountData.roles =
              this.accountData.roles.concat('trainee_pro');
          });
        },
        disabled: () => this.accountData?.roles?.includes('trainee_pro'),
      },
      {
        label: 'auth.demote-from-pro',
        handler: () => {
          this.traineeService.demoteFromPro(this.trainee.id).subscribe(() => {
            this.toastService.showInfoToast('auth.demote-from-pro-success');
            this.accountData.roles = this.accountData.roles.filter(
              (r) => r !== 'trainee_pro',
            );
          });
        },
        disabled: () => !this.accountData?.roles?.includes('trainee_pro'),
      },
      this.subscriptionService.getSubscriptionButton(this.trainee),
      {
        label: 'payments.subscription-plan',
        handler: () => {
          this.subscriptionPlanService
            .createSetManualSubscriptionPlanModal(this.trainee.id)
            .subscribe((plan) => {
              this.accountData.attributes.subscriptionPlan = plan;
            });
        },
        roles: ['admin'],
      },
    ]);
  }

  handleInstagramClick() {
    window.open(
      'https://www.instagram.com/' + this.trainee.instagram,
      '_blank',
    );
  }

  private fetchTraineeInfo(traineeId: string) {
    this.traineeService.getById(traineeId).subscribe((trainee) => {
      this.trainee = trainee;
    });

    this.traineeService
      .getLatestFitnessStateByTraineeId(traineeId)
      .pipe(catchError((_) => EMPTY))
      .subscribe((fitnessState) => this.setFitnessState(fitnessState));

    this.fetchAccountData(traineeId);
  }

  private fetchAccountData(traineeId: string) {
    this.traineeService.getAccountData(traineeId).subscribe((accountData) => {
      this.accountData = accountData;
    });
  }

  private setFitnessState(fitnessState: FitnessState) {
    this.fitnessState = fitnessState;
  }

  private createCreateFitnessStateModal(
    fitnessState: FitnessState = null,
  ): Observable<FitnessState> {
    return this.modalService.create<FitnessState>(
      {
        component: CreateFitnessStateModalComponent,
        componentProps: {
          traineeId: this.trainee.id,
          fitnessState,
        },
        canDismiss: true,
      },
      'large',
    );
  }
}

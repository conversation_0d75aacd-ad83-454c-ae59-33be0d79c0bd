<mpg-modal-layout
  [button]="modalButton"
  [title]="fitnessState ? 'Edit Fitness State' : 'New Fitness State'"
>
  <form [formGroup]="createFitnessStateFormGroup">
    <ion-item>
      <ion-input
        formControlName="bodyWeightInKg"
        label="Bodyweight (kg)"
        labelPlacement="floating"
        type="number"
      ></ion-input>
    </ion-item>
    <ion-item>
      <ion-label position="floating">Body fat %</ion-label>
      <ion-input formControlName="bodyFatPercentage" type="number"></ion-input>
    </ion-item>
    <ion-item>
      <ion-label>Physical Activity Level</ion-label>
      <ion-select formControlName="physicalActivityLevel">
        <ion-select-option
          *ngFor="let level of PhysicalActivityLevel | keyvalue"
          value="{{ level.key }}"
        >
          {{ level.value }}
        </ion-select-option>
      </ion-select>
    </ion-item>
    <ion-item>
      <ion-label position="floating">Thermic effect of food factor</ion-label>
      <ion-range
        class="ion-padding-top ion-margin-top"
        color="primary"
        formControlName="thermicEffectOfFoodFactor"
        max="1.25"
        min="1"
        step=".01"
      >
        <ion-label class="label--lg" slot="end">{{
          createFitnessStateFormGroup.get("thermicEffectOfFoodFactor").value
            | number : "1.2-2"
        }}</ion-label>
      </ion-range>
    </ion-item>
    <ion-item>
      <ion-label position="floating">Training session duration (min)</ion-label>
      <ion-input
        formControlName="trainingSessionDurationInMin"
        type="number"
      ></ion-input>
    </ion-item>
    <ion-item>
      <ion-label position="floating">Energy balance factor</ion-label>
      <ion-range
        class="ion-padding-top ion-margin-top"
        color="primary"
        formControlName="energyBalanceFactor"
        max="1.4"
        min="0.6"
        step=".01"
      >
        <ion-label class="label--lg" slot="end">{{
          createFitnessStateFormGroup.get("energyBalanceFactor").value
            | number : "1.2-2"
        }}</ion-label>
      </ion-range>
    </ion-item>
    <ion-item>
      <ion-label position="floating">Training days per week</ion-label>
      <ion-range
        class="ion-padding-top ion-margin-top"
        color="primary"
        formControlName="trainingDaysPerWeek"
        max="7"
        min="1"
        step="1"
      >
        <ion-label class="label--lg" slot="end">{{
          createFitnessStateFormGroup.get("trainingDaysPerWeek").value
            | number : "1.0-0"
        }}</ion-label>
      </ion-range>
    </ion-item>
  </form>
</mpg-modal-layout>

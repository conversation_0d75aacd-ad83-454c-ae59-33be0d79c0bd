import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';
import { CreateWorkoutExerciseModalComponent } from './create-workout-exercise-modal.component';
import { EntitySelectModule } from '../../../shared/components/entity-select/entity-select.module';
import { TranslateModule } from '@ngx-translate/core';
import { TimeFormatPipeModule } from '../../../shared/pipes/time-format/time-format-pipe.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ReactiveFormsModule,
    ModalLayoutModule,
    EntitySelectModule,
    TranslateModule,
    TimeFormatPipeModule,
  ],
  declarations: [CreateWorkoutExerciseModalComponent],
  exports: [CreateWorkoutExerciseModalComponent],
})
export class CreateWorkoutExerciseModalModule {}

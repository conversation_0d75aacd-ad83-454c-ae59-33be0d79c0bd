<input
  #videoInput
  (change)="handleVideoSelect($event)"
  accept="video/*"
  class="invisible"
  type="file"
/>
<ng-container
  *ngFor="let setRecord of exerciseRecord.setRecords; let i = index"
>
  <ion-item-sliding #itemSliding [disabled]="setRecordVideo || setRecord.video">
    <ion-item
      [formGroup]="setRecordsFormGroupMap[setRecord.id]"
      class="transparent no-end-padding"
    >
      <ion-col class="flex-centered" size="1">
        <ion-label *ngIf="setRecordsFormGroupMap[setRecord.id].invalid"
          >{{ i + 1 }}.
        </ion-label>
        <mpg-loading-checkmark
          *ngIf="setRecordsFormGroupMap[setRecord.id].valid"
          [color]="color"
          [isLoading]="
            setRecordsLoadingMap && setRecordsLoadingMap[setRecord.id]
          "
        ></mpg-loading-checkmark>
      </ion-col>
      <ion-col class="flex-centered" size="6">
        <ion-input
          aria-labelledby="asd"
          class="ion-text-center bolder small"
          formControlName="weightInKg"
          maxlength="3"
          mpgMaxLength
          type="number"
        >
          <ion-chip class="label" slot="label">
            <ion-label>{{ "training.weight" | translate }}</ion-label>
          </ion-chip>
        </ion-input>
      </ion-col>
      <ion-col class="flex-centered" size="5">
        <ion-input
          aria-labelledby="asd"
          class="ion-text-center bolder small"
          formControlName="reps"
          maxlength="3"
          mpgMaxLength
          type="number"
        >
          <ion-chip class="label" slot="label">
            <ion-label>{{ "training.reps" | translate }}</ion-label>
          </ion-chip>
        </ion-input>
      </ion-col>
    </ion-item>
    <ion-item-options>
      <ion-item-option
        (click)="
          videoInput.click();
          this.selectedSetRecord = setRecord;
          itemSliding.close()
        "
      >
        <ion-icon color="light" name="videocam" slot="icon-only"></ion-icon>
      </ion-item-option>
    </ion-item-options>
  </ion-item-sliding>
  <ion-item
    (click)="handleSetRecordVideoClick(setRecord)"
    *ngIf="setRecord.video"
    [button]="true"
    class="transparent w-full ion-text-center"
  >
    <ion-col class="flex-centered" size="1">
      <ion-icon color="dark" name="videocam"></ion-icon>
    </ion-col>
    <ion-col class="flex-centered" size="11">
      <ion-label>{{ "video" | translate }}</ion-label>
    </ion-col>
  </ion-item>
  <ion-item *ngIf="setRecordVideo && selectedSetRecord?.id === setRecord.id">
    <ion-progress-bar
      *ngIf="setRecordVideo.progress < 100"
      [value]="setRecordVideo.progress / 100"
    ></ion-progress-bar>
  </ion-item>
</ng-container>

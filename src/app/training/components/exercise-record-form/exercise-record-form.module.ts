import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { ExerciseRecordFormComponent } from './exercise-record-form.component';
import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';
import { MaxLengthDirectiveModule } from '../../../shared/directives/max-length/max-length-directive.module';
import { TranslateModule } from '@ngx-translate/core';
import { LoadingCheckmarkComponent } from '../../../shared/components/loading-checkmark/loading-checkmark.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ReactiveFormsModule,
    ModalLayoutModule,
    MaxLengthDirectiveModule,
    TranslateModule,
  ],
  declarations: [ExerciseRecordFormComponent, LoadingCheckmarkComponent],
  exports: [ExerciseRecordFormComponent],
})
export class ExerciseRecordFormModule {}

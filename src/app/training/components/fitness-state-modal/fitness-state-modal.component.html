<mpg-modal-layout
  [button]="modalButton"
  [popoverMenuButtons]="modalPopoverMenuButtons"
  title="Fitness State"
>
  <ion-list class="ion-padding">
    <ion-row>
      <ion-col
        *ngFor="let pair of fitnessStateMap | keyvalue : fitnessStateMapOrder"
        size-md="6"
      >
        <ion-item>
          <ion-label class="ion-text-center"
            >{{ pair.key }}
            <ion-chip>
              <ion-label>{{ pair.value }}</ion-label>
            </ion-chip>
          </ion-label>
        </ion-item>
      </ion-col>
    </ion-row>
  </ion-list>
</mpg-modal-layout>

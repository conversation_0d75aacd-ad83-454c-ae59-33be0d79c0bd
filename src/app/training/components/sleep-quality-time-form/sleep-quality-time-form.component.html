<div class="w-full flex-space-around ion-margin-bottom">
  <div
    (click)="bedtimeModal.present()"
    [ngClass]="{ disabled: !sleepQualityRecord?.bedtime }"
    class="time-container ion-activatable overflow-hidden"
  >
    <ion-ripple-effect></ion-ripple-effect>
    <ion-icon src="/assets/icon/bedtime.svg"></ion-icon>
    <ion-datetime-button
      [ngClass]="{ disabled: !sleepQualityRecord?.bedtime }"
      color="dark"
      datetime="bedtime"
    ></ion-datetime-button>
  </div>
  <div
    (click)="wakeUpTimeModal.present()"
    [ngClass]="{ disabled: !sleepQualityRecord?.wakeUpTime }"
    class="time-container ion-activatable overflow-hidden"
  >
    <ion-ripple-effect></ion-ripple-effect>
    <ion-icon src="/assets/icon/wake-up-time.svg"></ion-icon>
    <ion-datetime-button
      [ngClass]="{ disabled: !sleepQualityRecord?.wakeUpTime }"
      color="dark"
      datetime="wake-up-time"
    ></ion-datetime-button>
  </div>
</div>

<form [formGroup]="sleepQualityFormGroup">
  <ion-modal #bedtimeModal [keepContentsMounted]="true">
    <ng-template>
      <ion-datetime
        [cancelText]="'buttons.cancel' | translate"
        [clearText]="'buttons.delete' | translate"
        [doneText]="'buttons.set' | translate"
        [locale]="locale$ | async"
        [minuteValues]="[0, 15, 30, 45]"
        [showClearButton]="true"
        [showDefaultButtons]="true"
        formControlName="bedtime"
        id="bedtime"
        presentation="time"
      ></ion-datetime>
    </ng-template>
  </ion-modal>
  <ion-modal #wakeUpTimeModal [keepContentsMounted]="true">
    <ng-template>
      <ion-datetime
        [cancelText]="'buttons.cancel' | translate"
        [clearText]="'buttons.delete' | translate"
        [doneText]="'buttons.set' | translate"
        [locale]="locale$ | async"
        [minuteValues]="[0, 15, 30, 45]"
        [showClearButton]="true"
        [showDefaultButtons]="true"
        formControlName="wakeUpTime"
        id="wake-up-time"
        presentation="time"
      ></ion-datetime>
    </ng-template>
  </ion-modal>
</form>

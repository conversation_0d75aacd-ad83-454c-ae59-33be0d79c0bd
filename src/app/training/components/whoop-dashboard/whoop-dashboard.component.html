<div class="whoop-container p-relative">
  <mpg-screenshot-button></mpg-screenshot-button>
  <div *ngIf="sleepQualityRecord.whoopRecord?.sleep" class="metrics">
    <mpg-whoop-chart
      [percentage]="
        sleepQualityRecord.whoopRecord?.sleep?.score
          ?.sleep_performance_percentage
      "
      type="sleep"
    ></mpg-whoop-chart>
    <mpg-whoop-chart
      *ngIf="sleepQualityRecord.whoopRecord?.recovery"
      [percentage]="
        sleepQualityRecord.whoopRecord?.recovery?.score?.recovery_score
      "
      type="recovery"
    ></mpg-whoop-chart>
  </div>

  <div class="flex-space-around">
    <div class="sleep-container flex-column-centered">
      <h2 class="hours">
        {{
          sleepQualityRecord.whoopRecord.sleep.score.stage_summary
            .total_in_bed_time_milli -
            sleepQualityRecord.whoopRecord.sleep.score.stage_summary
              .total_awake_time_milli | date: "HH:mm" : "UTC"
        }}
      </h2>
      <div class="cta">
        <h2>HOURS OF SLEEP</h2>
      </div>
    </div>
    <div class="sleep-debt-container flex-column-centered">
      <h2 class="hours">
        {{ sleepNeeded | date: "HH:mm" : "UTC" }}
      </h2>
      <div class="cta">
        <h2>SLEEP NEEDED</h2>
      </div>
    </div>
  </div>
  <div
    (click)="handleHistory()"
    *ngIf="showHistoryButton"
    class="cta history ion-activatable overflow-hidden"
  >
    <ion-ripple-effect></ion-ripple-effect>
    <h2>{{ "history" | translate | uppercase }}</h2>
  </div>
  <div class="attribution">
    <h2>DATA BY</h2>
    <img src="/assets/icon/whoop-wordmark.svg" width="120px" />
  </div>
</div>

<mpg-card
  [button]="getButton()"
  [popoverMenuButtons]="popoverMenuButtons"
  [title]="title"
>
  <div
    (click)="hidePersonalRecords = true; onPRMessageClear.emit()"
    *ngIf="finished && totalPersonalRecordsCount > 0"
    [ngClass]="{ hidden: hidePersonalRecords }"
    class="pr-message"
  >
    <ion-text class="ion-text-center bold" color="light">
      <ion-icon name="trophy" size="large"></ion-icon>
      <h2 *ngIf="totalPersonalRecordsCount === 1">
        {{ "training.pr-message" | translate }}
      </h2>
      <h2 *ngIf="totalPersonalRecordsCount > 1">
        {{
          "training.prs-message"
            | translate: { count: totalPersonalRecordsCount }
        }}
      </h2>
    </ion-text>
  </div>
  <ion-item class="ion-no-padding no-end-padding" color="dark" lines="none">
    <ion-col size="12">
      <mpg-workout-record-chips
        [workoutRecord]="workoutRecord"
        class="flex-centered flex-wrap"
      ></mpg-workout-record-chips>
    </ion-col>
  </ion-item>
  <mpg-workout-record-notes
    [workoutRecord]="workoutRecord"
  ></mpg-workout-record-notes>
  <mpg-workout-record-table
    [workoutRecord]="workoutRecord"
  ></mpg-workout-record-table>
</mpg-card>

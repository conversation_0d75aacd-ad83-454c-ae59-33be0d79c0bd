<div *ngIf="!workoutRecord.exerciseRecords?.length">
  <ion-text class="ion-margin-bottom ion-padding" color="dark"
    ><h2>No exercises in record.</h2></ion-text
  >
</div>
<div *ngIf="workoutRecord.exerciseRecords?.length">
  <ion-row class="border-bottom">
    <ion-col class="ion-padding" size="2">
      <ion-text color="dark"><h2>№</h2></ion-text>
    </ion-col>
    <ion-col class="ion-padding" size="4">
      <ion-text color="dark"
        ><h2>{{ "training.exercise" | translate }}</h2></ion-text
      >
    </ion-col>
    <ion-col class="ion-padding" size="6">
      <ion-text color="dark"
        ><h2>{{ "training.sets" | translate }}</h2></ion-text
      >
    </ion-col>
  </ion-row>
  <ng-container
    *ngFor="
      let exerciseRecord of workoutRecord.exerciseRecords;
      let last = last;
      let index = index
    "
  >
    <ion-row
      [ngClass]="{
        'border-bottom':
          !last &&
          !lastInGroup(exerciseRecord, index) &&
          !hasNotes(exerciseRecord) &&
          !hasVideos(exerciseRecord)
      }"
    >
      <ion-item-sliding #itemSliding>
        <ion-item
          [lines]="
            hasNotes(exerciseRecord) || hasVideos(exerciseRecord)
              ? 'none'
              : undefined
          "
          class="ion-no-padding ion-text-center"
        >
          <ion-col size="2">
            <ion-text color="dark"
              ><h2>
                {{ getOrderNumber(exerciseRecord.exercise) }}
              </h2>
            </ion-text>
          </ion-col>
          <ion-col size="4">
            <ion-text color="dark"
              ><h2>
                {{
                  "exercises." + exerciseRecord.exercise.exercise.id
                    | translate
                      : { fallback: exerciseRecord.exercise.exercise.name }
                }}
              </h2>
            </ion-text>
          </ion-col>
          <ion-col size="6">
            <ion-text
              *ngIf="
                !exerciseRecord.isSkipped &&
                exerciseRecord.personalRecordsCount === 0
              "
              color="dark"
              ><h2>{{ exerciseRecord.setRecordsAsText }}</h2></ion-text
            >
            <ion-chip *ngIf="exerciseRecord.isSkipped" class="dark"
              >{{ "skipped" | translate }}
            </ion-chip>
            <ion-chip
              (click)="handlePersonalRecordsClick(exerciseRecord)"
              *ngIf="exerciseRecord.personalRecordsCount > 0"
              class="dark overflow-auto pr-container"
              >{{ exerciseRecord.setRecordsAsText }}

              <ion-chip class="pr gold"> PR </ion-chip>
            </ion-chip>
          </ion-col>
        </ion-item>
        <ion-item-options>
          <ion-item-option
            (click)="
              handleExerciseRecordPopoverMenu(
                $event,
                itemSliding,
                exerciseRecord,
                workoutRecord
              )
            "
            class="transparent"
          >
            <ion-icon
              color="dark"
              name="ellipsis-vertical"
              slot="icon-only"
            ></ion-icon>
          </ion-item-option>
        </ion-item-options>
      </ion-item-sliding>
    </ion-row>
    <ion-row
      *ngIf="hasVideos(exerciseRecord)"
      [ngClass]="{
        'border-bottom':
          !last &&
          !lastInGroup(exerciseRecord, index) &&
          !hasNotes(exerciseRecord)
      }"
    >
      <ion-item
        class="w-full ion-no-padding ion-text-center small"
        lines="none"
      >
        <ion-col class="flex-centered ion-no-padding" size="12">
          <ion-chip
            (click)="handleVideoClick(setRecord)"
            *ngFor="let setRecord of getVideoSetRecords(exerciseRecord)"
            class="dark"
          >
            <ion-icon color="light" name="videocam"></ion-icon>
            <ion-label
              >{{ "training.set" | translate }}
              {{ setRecord.orderNumber }}
            </ion-label>
          </ion-chip>
        </ion-col>
      </ion-item>
    </ion-row>
    <ion-row
      *ngIf="hasNotes(exerciseRecord)"
      [ngClass]="{
        'border-bottom': !last && !lastInGroup(exerciseRecord, index)
      }"
    >
      <ion-item
        (click)="handleNotesClick(exerciseRecord)"
        [button]="true"
        [ngClass]="{ small: hasVideos(exerciseRecord) }"
        class="w-full ion-no-padding ion-text-center"
      >
        <ion-col size="2">
          <ion-icon color="dark" name="document-text"></ion-icon>
        </ion-col>
        <ion-col class="flex-column-space-around" size="10">
          <ion-text *ngFor="let note of exerciseRecord.notes"
            >{{ note.content }}
          </ion-text>
        </ion-col>
      </ion-item>
    </ion-row>
  </ng-container>
</div>

import { Component, Input, OnInit } from '@angular/core';
import {
  WorkoutExercise,
  WorkoutExerciseRecord,
  WorkoutExerciseSetRecord,
  WorkoutRecord,
} from '../../models';
import { ModalService, PopoverService } from '../../../shared/services';
import {
  ExerciseService,
  WorkoutExerciseRecordService,
  WorkoutExerciseSetRecordService,
  WorkoutRecordService,
  WorkoutService,
} from '../../services';
import { IonItemSliding } from '@ionic/angular';
import { ExerciseRecordModalComponent } from '../exercise-record-modal/exercise-record-modal.component';
import { switchMap } from 'rxjs';
import { Note } from '../../../shared/models';

@Component({
  selector: 'mpg-workout-record-table',
  templateUrl: './workout-record-table.component.html',
  styleUrls: ['./workout-record-table.component.scss'],
})
export class WorkoutRecordTableComponent implements OnInit {
  @Input() workoutRecord: WorkoutRecord;
  @Input() traineeId: string;

  constructor(
    private popoverService: PopoverService,
    private exerciseService: ExerciseService,
    private workoutService: WorkoutService,
    private workoutRecordService: WorkoutRecordService,
    private workoutExerciseRecordService: WorkoutExerciseRecordService,
    private workoutExerciseSetRecordService: WorkoutExerciseSetRecordService,
    private modalService: ModalService,
  ) {}

  ngOnInit() {}

  getOrderNumber(exercise: WorkoutExercise) {
    return this.workoutService.getOrderNumber(exercise);
  }

  async handleExerciseRecordPopoverMenu(
    event: MouseEvent,
    itemSliding: IonItemSliding,
    exerciseRecord: WorkoutExerciseRecord,
    workoutRecord: WorkoutRecord,
  ) {
    await this.popoverService.handlePopoverMenu(
      event,
      [
        this.exerciseService.getDetailsButton(exerciseRecord.exercise.exercise),
        {
          label: 'training.change-exercise',
          handler: () => {
            this.workoutRecordService
              .createChangeExerciseInExerciseRecordModal(
                this.workoutRecord.id,
                exerciseRecord.id,
              )
              .subscribe((newExerciseRecord) => {
                this.workoutRecord.exerciseRecords =
                  this.workoutRecord.exerciseRecords.map((er) => {
                    if (er.id === newExerciseRecord.id) {
                      return newExerciseRecord;
                    }

                    return er;
                  });
              });
          },
        },
        {
          label: 'buttons.edit',
          handler: () => {
            this.modalService
              .create(
                {
                  component: ExerciseRecordModalComponent,
                  componentProps: {
                    workoutRecord,
                    exerciseRecord,
                  },
                  backdropDismiss: false,
                },
                'small',
              )
              .pipe(
                switchMap(() => {
                  return this.workoutRecordService.getExerciseRecord(
                    workoutRecord.id,
                    exerciseRecord.id,
                  );
                }),
              )
              .subscribe((newExerciseRecord) => {
                this.workoutRecord.exerciseRecords =
                  this.workoutRecord.exerciseRecords.map((er) => {
                    if (er.id === newExerciseRecord.id) {
                      return newExerciseRecord;
                    }

                    return er;
                  });
              });
          },
        },
        {
          label: 'history',
          handler: () => {
            this.workoutExerciseRecordService.showExerciseRecordsHistoryModal(
              {
                workoutExercises: [exerciseRecord.exercise],
                filter: 'exercise',
              },
              this.traineeId,
            );
          },
        },
        {
          label: 'training.sets-history.prs',
          handler: () => {
            this.workoutExerciseSetRecordService.showPRSetRecordsModal({
              workoutExercise: exerciseRecord.exercise,
              traineeId: this.traineeId,
              workoutExerciseRecordId: exerciseRecord.id,
              endDate: workoutRecord.startedOn,
            });
          },
        },
        this.workoutExerciseRecordService.getShareButton(exerciseRecord),
        this.workoutRecordService.getExerciseRecordNotesButton(
          exerciseRecord,
          (newNotes: Note[]) => {
            exerciseRecord.notes = newNotes;
          },
        ),
        ...this.workoutRecordService.getSkipExerciseButtons(
          workoutRecord,
          exerciseRecord,
        ),
      ],
      () => {
        itemSliding.close();
      },
    );
  }

  handleNotesClick(exerciseRecord: WorkoutExerciseRecord) {
    this.workoutRecordService
      .getExerciseRecordNotesButton(exerciseRecord, (newNotes: Note[]) => {
        exerciseRecord.notes = newNotes;
      })
      .handler();
  }

  lastInGroup(exerciseRecord: WorkoutExerciseRecord, index: number) {
    return (
      this.workoutRecord.exerciseRecords[index + 1] &&
      this.workoutRecord.exerciseRecords[index + 1].exercise.mainOrderNumber ===
        exerciseRecord.exercise.mainOrderNumber
    );
  }

  hasNotes(exerciseRecord: WorkoutExerciseRecord) {
    return exerciseRecord.notes.length > 0;
  }

  hasVideos(exerciseRecord: WorkoutExerciseRecord) {
    return exerciseRecord.setRecords.some((sr) => !!sr.video);
  }

  getVideoSetRecords(exerciseRecord: WorkoutExerciseRecord) {
    return exerciseRecord.setRecords.filter((sr) => !!sr.video);
  }

  handleVideoClick(setRecord: WorkoutExerciseSetRecord) {
    this.workoutExerciseSetRecordService.showSetRecordVideoModal(
      setRecord,
      () => {
        setRecord.video = undefined;
      },
    );
  }

  handlePersonalRecordsClick(exerciseRecord: WorkoutExerciseRecord) {
    this.workoutExerciseSetRecordService.showPRSetRecordsModal({
      workoutExercise: exerciseRecord.exercise,
      traineeId: this.traineeId,
      workoutExerciseRecordId: exerciseRecord.id,
      endDate: this.workoutRecord.startedOn,
    });
  }
}

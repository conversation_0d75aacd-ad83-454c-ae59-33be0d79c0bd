import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, switchMap } from 'rxjs';
import { environment } from '../../../environments/environment';
import {
  Gym,
  GymCreateRequest,
  GymEditRequest,
  WorkoutRecord,
} from '../models';
import { TraineeService } from './trainee.service';
import { map, take } from 'rxjs/operators';
import { ModalService, ToastService } from '../../shared/services';
import { Button } from '../../shared/models';
import { WorkoutRecordService } from './workout-record.service';
import { SubscriptionPlanType } from '../../payments/enumerations';

@Injectable({
  providedIn: 'root',
})
export class GymService {
  static readonly BASE_URL = `${environment.TRAINING_SERVICE_API_URL}/v1/gyms`;

  constructor(
    private http: HttpClient,
    private traineeService: TraineeService,
    private modalService: ModalService,
    private toastService: ToastService,
    private workoutRecordService: WorkoutRecordService,
  ) {}

  getAll(): Observable<Gym[]> {
    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((traineeId) =>
        this.http.get<Gym[]>(`${GymService.BASE_URL}?traineeId=${traineeId}`),
      ),
    );
  }

  create(model: GymCreateRequest): Observable<Gym> {
    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((traineeId) =>
        this.http.post<Gym>(GymService.BASE_URL, {
          ...model,
          traineeId,
        }),
      ),
    );
  }

  delete(id: string): Observable<void> {
    return this.http.delete<void>(`${GymService.BASE_URL}/${id}`);
  }

  edit(id: string, model: GymEditRequest): Observable<void> {
    return this.http.patch<void>(`${GymService.BASE_URL}/${id}`, model);
  }

  createGymsModal(selectedGym?: Gym): Observable<Gym> {
    return this.getAll().pipe(
      switchMap((gyms) => {
        return this.modalService.createEntityListModal<Gym>({
          title: 'training.select-gym',
          entityTextProp: 'name',
          entities: gyms,
          createButtonLabel: 'training.create-gym',
          inputType: 'text',
          selectMode: true,
          selectedEntity: gyms.find((g) => g.id === selectedGym?.id),
          chipLabel: 'default',
          chipPredicate: (gym: Gym) => gym.isDefault,
          onCreate: (model: GymCreateRequest) => {
            return this.create(model);
          },
          onEdit: (id: string, model: GymEditRequest) => {
            return this.edit(id, model);
          },
          onDelete: (id: string) => {
            return this.delete(id);
          },
          popoverMenuButtonsSupplier: (gym: Gym): Button[] => {
            return [
              {
                label: 'buttons.set-as-default',
                handler: () => {
                  this.traineeService.putDefaultGym(gym.id).subscribe(() => {
                    gyms.forEach((g) => (g.isDefault = false));
                    gym.isDefault = true;
                    this.toastService.showInfoToast(
                      'training.set-default-gym-success',
                    );
                  });
                },
                disabled: () => gym.isDefault,
              },
            ];
          },
        });
      }),
    );
  }

  getSelectGymButton(workoutRecord: WorkoutRecord): Button {
    return {
      label: 'training.select-gym',
      handler: () => {
        this.createGymsModal(workoutRecord.gym)
          .pipe(
            switchMap((gym: Gym) => {
              return this.workoutRecordService
                .edit(workoutRecord.id, {
                  gymId: gym.id,
                })
                .pipe(map(() => gym));
            }),
          )
          .subscribe((gym: Gym) => {
            workoutRecord.gym = gym;
            this.toastService.showInfoToast('training.select-gym-success');
          });
      },
      subscriptionPlan: SubscriptionPlanType.ADVANCED,
    };
  }
}

import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, switchMap } from 'rxjs';
import { environment } from '../../../environments/environment';
import { SleepQualityRecord, SleepQualityRecordPutRequest } from '../models';
import { Page } from '../../shared/models';
import { ModalService } from '../../shared/services';
import { SleepQualityHistoryModalComponent } from '../components/sleep-quality-history-modal/sleep-quality-history-modal.component';
import { TraineeService } from './trainee.service';
import { take } from 'rxjs/operators';
import { WhoopDashboardComponent } from '../components/whoop-dashboard/whoop-dashboard.component';

@Injectable({
  providedIn: 'root',
})
export class SleepQualityService {
  static readonly BASE_URL = `${environment.TRAINING_SERVICE_API_URL}/v1/sleep-quality-records`;

  constructor(
    private http: HttpClient,
    private modalService: ModalService,
    private traineeService: TraineeService,
  ) {}

  getRecord(traineeId: string): Observable<SleepQualityRecord> {
    const params = new HttpParams().append('traineeId', traineeId);

    return this.http.get<SleepQualityRecord>(SleepQualityService.BASE_URL, {
      params,
    });
  }

  getHistory({
    page = 1,
    traineeId,
    maxDate,
  }: {
    page: number;
    traineeId?: string;
    maxDate?: string;
  }): Observable<Page<SleepQualityRecord>> {
    let params = new HttpParams().append('page', page);
    if (maxDate) {
      params = params.append('maxDate', maxDate);
    }

    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((currentTraineeId) => {
        return this.http.get<Page<SleepQualityRecord>>(
          `${SleepQualityService.BASE_URL}/history?traineeId=${
            traineeId || currentTraineeId
          }`,
          {
            params,
          },
        );
      }),
    );
  }

  putRecord(
    traineeId: string,
    request: SleepQualityRecordPutRequest,
  ): Observable<SleepQualityRecord> {
    const params = new HttpParams().append('traineeId', traineeId);

    return this.http.put<SleepQualityRecord>(
      SleepQualityService.BASE_URL,
      request,
      { params },
    );
  }

  showHistoryModal({
    traineeId,
    highlightedDate,
  }: {
    traineeId?: string;
    highlightedDate?: string;
  } = {}) {
    this.modalService
      .create(
        {
          component: SleepQualityHistoryModalComponent,
          componentProps: { traineeId, highlightedDate },
        },
        'medium',
      )
      .subscribe();
  }

  showWhoopModal(sleepQualityRecord: SleepQualityRecord) {
    this.modalService
      .create(
        {
          component: WhoopDashboardComponent,
          componentProps: { sleepQualityRecord, showHistoryButton: false },
        },
        'small',
      )
      .subscribe();
  }
}

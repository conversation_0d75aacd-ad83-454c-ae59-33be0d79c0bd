import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import {
  BehaviorSubject,
  debounceTime,
  EMPTY,
  filter,
  Observable,
  switchMap,
} from 'rxjs';
import { environment } from '../../../environments/environment';
import {
  Exercise,
  ScheduledWorkout,
  TrainingVolumeHistory,
  Workout,
  WorkoutExerciseRecord,
  WorkoutExerciseRecordEditRequest,
  WorkoutExerciseSetRecord,
  WorkoutExerciseSetRecordEditRequest,
  WorkoutExerciseSetRecordVideo,
  WorkoutExerciseSetRecordVideoRequest,
  WorkoutRecord,
  WorkoutRecordCreateRequest,
  WorkoutRecordEditRequest,
  WorkoutsSchedule,
} from '../models';
import { TraineeService } from './trainee.service';
import { catchError, map, take, tap } from 'rxjs/operators';
import { Router } from '@angular/router';
import {
  <PERSON><PERSON>,
  <PERSON>,
  NoteRequest,
  Page,
  StorageObject,
  StorageObjectUploadUrlRequest,
} from '../../shared/models';
import { FormBuilder, Validators } from '@angular/forms';
import { ExerciseService } from './exercise.service';
import {
  AlertService,
  DateService,
  FileService,
  ModalService,
} from '../../shared/services';
import { WorkoutRecordModalComponent } from '../components/workout-record-modal/workout-record-modal.component';
import { TranslateService } from '@ngx-translate/core';
import { NoteService } from '../../shared/services/note.service';
import { TrainingVolumeHistoryModalComponent } from '../components/training-volume-history-modal/training-volume-history-modal.component';

@Injectable({
  providedIn: 'root',
})
export class WorkoutRecordService {
  static readonly BASE_URL = `${environment.TRAINING_SERVICE_API_URL}/v1/workout-records`;

  private readonly activeWorkoutRecordSubject$ =
    new BehaviorSubject<WorkoutRecord>(null);

  readonly activeWorkoutRecord$ =
    this.activeWorkoutRecordSubject$.asObservable();

  constructor(
    private http: HttpClient,
    private traineeService: TraineeService,
    private exerciseService: ExerciseService,
    private alertService: AlertService,
    private router: Router,
    private fb: FormBuilder,
    private modalService: ModalService,
    private translateService: TranslateService,
    private dateService: DateService,
    private fileService: FileService,
    private noteService: NoteService,
  ) {
    this.traineeService.trainee$
      .pipe(switchMap(() => this.getActive()))
      .subscribe();
  }

  create(model: WorkoutRecordCreateRequest): Observable<WorkoutRecord> {
    return this.http.post<WorkoutRecord>(WorkoutRecordService.BASE_URL, model);
  }

  get(id: string): Observable<WorkoutRecord> {
    return this.http.get<WorkoutRecord>(
      `${WorkoutRecordService.BASE_URL}/${id}`,
    );
  }

  edit(id: string, model: WorkoutRecordEditRequest): Observable<void> {
    return this.http.patch<void>(
      `${WorkoutRecordService.BASE_URL}/${id}`,
      model,
    );
  }

  delete(id: string): Observable<void> {
    return this.http.delete<void>(`${WorkoutRecordService.BASE_URL}/${id}`);
  }

  getActive(): Observable<WorkoutRecord> {
    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((traineeId) =>
        this.http.get<WorkoutRecord>(
          `${WorkoutRecordService.BASE_URL}/active?traineeId=${traineeId}`,
        ),
      ),
      tap((workoutRecord) => {
        this.activeWorkoutRecordSubject$.next(workoutRecord);
      }),
    );
  }

  getNext(): Observable<ScheduledWorkout> {
    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((traineeId) =>
        this.http.get<ScheduledWorkout>(
          `${WorkoutRecordService.BASE_URL}/next?traineeId=${traineeId}`,
        ),
      ),
    );
  }

  getSchedule(): Observable<WorkoutsSchedule> {
    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((traineeId) =>
        this.http.get<WorkoutsSchedule>(
          `${WorkoutRecordService.BASE_URL}/schedule?traineeId=${traineeId}`,
        ),
      ),
    );
  }

  getHistory(
    page: number = 1,
    size: number = 10,
    basicInfo: boolean = false,
  ): Observable<Page<WorkoutRecord>> {
    const params = new HttpParams()
      .append('page', page)
      .append('size', size)
      .append('basicInfo', basicInfo);

    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((traineeId) =>
        this.http.get<Page<WorkoutRecord>>(
          `${WorkoutRecordService.BASE_URL}?traineeId=${traineeId}`,
          { params },
        ),
      ),
    );
  }

  getTrainingVolumeHistory(date: string): Observable<TrainingVolumeHistory> {
    const params = new HttpParams()
      .append('endDate', date)
      .append('period', 'WEEK');

    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((traineeId) =>
        this.http.get<TrainingVolumeHistory>(
          `${WorkoutRecordService.BASE_URL}/training-volume/stats?traineeId=${traineeId}`,
          { params },
        ),
      ),
    );
  }

  endActive(): Observable<WorkoutRecord> {
    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((traineeId) =>
        this.http.post<WorkoutRecord>(
          `${WorkoutRecordService.BASE_URL}/active/end?traineeId=${traineeId}`,
          null,
        ),
      ),
      tap(() => this.activeWorkoutRecordSubject$.next(null)),
    );
  }

  cancelActive(): Observable<void> {
    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((traineeId) =>
        this.http.delete<void>(
          `${WorkoutRecordService.BASE_URL}/active/cancel?traineeId=${traineeId}`,
        ),
      ),
      tap(() => this.activeWorkoutRecordSubject$.next(null)),
    );
  }

  createNote(workoutRecordId: string, model: NoteRequest): Observable<Note> {
    return this.http.post<Note>(
      `${WorkoutRecordService.BASE_URL}/${workoutRecordId}/notes`,
      model,
    );
  }

  getExerciseRecord(
    workoutRecordId: string,
    exerciseRecordId: string,
  ): Observable<WorkoutExerciseRecord> {
    return this.http.get<WorkoutExerciseRecord>(
      `${WorkoutRecordService.BASE_URL}/${workoutRecordId}/exercise-records/${exerciseRecordId}`,
    );
  }

  editExerciseRecord(
    workoutRecordId: string,
    exerciseRecordId: string,
    model: WorkoutExerciseRecordEditRequest,
  ): Observable<WorkoutExerciseRecord> {
    return this.http.patch<WorkoutExerciseRecord>(
      `${WorkoutRecordService.BASE_URL}/${workoutRecordId}/exercise-records/${exerciseRecordId}`,
      model,
    );
  }

  skipExerciseRecord(
    workoutRecordId: string,
    exerciseRecordId: string,
  ): Observable<WorkoutExerciseRecord> {
    return this.http.post<WorkoutExerciseRecord>(
      `${WorkoutRecordService.BASE_URL}/${workoutRecordId}/exercise-records/${exerciseRecordId}/skip`,
      null,
    );
  }

  cancelSkipExerciseRecord(
    workoutRecordId: string,
    exerciseRecordId: string,
  ): Observable<WorkoutExerciseRecord> {
    return this.http.post<WorkoutExerciseRecord>(
      `${WorkoutRecordService.BASE_URL}/${workoutRecordId}/exercise-records/${exerciseRecordId}/skip/cancel`,
      null,
    );
  }

  createExerciseRecordNote(
    workoutRecordId: string,
    exerciseRecordId: string,
    model: NoteRequest,
  ): Observable<Note> {
    return this.http.post<Note>(
      `${WorkoutRecordService.BASE_URL}/${workoutRecordId}/exercise-records/${exerciseRecordId}/notes`,
      model,
    );
  }

  createSetRecord(
    id: string,
    exerciseRecordId: string,
  ): Observable<WorkoutExerciseSetRecord> {
    return this.http.post<WorkoutExerciseSetRecord>(
      `${WorkoutRecordService.BASE_URL}/${id}/exercise-records/${exerciseRecordId}/set-records`,
      null,
    );
  }

  editSetRecord(
    id: string,
    exerciseRecordId: string,
    setRecordId: string,
    model: WorkoutExerciseSetRecordEditRequest,
  ): Observable<void> {
    return this.http.put<void>(
      `${WorkoutRecordService.BASE_URL}/${id}/exercise-records/${exerciseRecordId}/set-records/${setRecordId}`,
      model,
    );
  }

  deleteSetRecord(
    id: string,
    exerciseRecordId: string,
    setRecordId: string,
  ): Observable<void> {
    return this.http.delete<void>(
      `${WorkoutRecordService.BASE_URL}/${id}/exercise-records/${exerciseRecordId}/set-records/${setRecordId}`,
    );
  }

  getSetRecordVideoUploadUrl: (
    id: string,
    exerciseRecordId: string,
    setRecordId: string,
  ) => (
    storageObjectUploadUrlRequest: StorageObjectUploadUrlRequest,
  ) => Observable<StorageObject> = (id, exerciseRecordId, setRecordId) => {
    return (model) => {
      return this.http.post<StorageObject>(
        `${WorkoutRecordService.BASE_URL}/${id}/exercise-records/${exerciseRecordId}/set-records/${setRecordId}/video/upload-url`,
        model,
      );
    };
  };

  putSetRecordVideo(
    id: string,
    exerciseRecordId: string,
    setRecordId: string,
    model: WorkoutExerciseSetRecordVideoRequest,
  ): Observable<WorkoutExerciseSetRecordVideo> {
    return this.http.put<WorkoutExerciseSetRecordVideo>(
      `${WorkoutRecordService.BASE_URL}/${id}/exercise-records/${exerciseRecordId}/set-records/${setRecordId}/video`,
      model,
    );
  }

  deleteSetRecordVideo(
    id: string,
    exerciseRecordId: string,
    setRecordId: string,
  ): Observable<void> {
    return this.http.delete<void>(
      `${WorkoutRecordService.BASE_URL}/${id}/exercise-records/${exerciseRecordId}/set-records/${setRecordId}/video`,
    );
  }

  createSetRecordVideoNote(
    id: string,
    exerciseRecordId: string,
    setRecordId: string,
    model: NoteRequest,
  ): Observable<Note> {
    return this.http.post<Note>(
      `${WorkoutRecordService.BASE_URL}/${id}/exercise-records/${exerciseRecordId}/set-records/${setRecordId}/video/notes`,
      model,
    );
  }

  getWorkoutButton(
    activeWorkoutRecord: WorkoutRecord,
    workout: Workout,
  ): Button {
    if (activeWorkoutRecord) {
      if (activeWorkoutRecord.workout.id === workout.id) {
        return {
          label: 'training.resume-workout',
          handler: () => {
            this.router.navigateByUrl('/trainee/active-workout');
          },
        };
      }

      return null;
    }

    return {
      label: 'training.start-workout',
      handler: () => {
        this.create({ workoutId: workout.id })
          .pipe(switchMap(() => this.getActive()))
          .subscribe(() => {
            this.router.navigateByUrl('/trainee/active-workout');
          });
      },
    };
  }

  getSetRecordFormGroups({
    workoutRecord,
    exerciseRecord,
    loadingCallback,
    finishedCallback,
  }: {
    workoutRecord: WorkoutRecord;
    exerciseRecord: WorkoutExerciseRecord;
    loadingCallback?: (id?: string) => void;
    finishedCallback?: (req?: WorkoutExerciseSetRecordEditRequest) => void;
  }) {
    return exerciseRecord.setRecords.map((s) => {
      const formGroup = this.fb.group({
        id: [s.id, [Validators.required]],
        reps: [s?.reps || null, [Validators.required, Validators.min(0.5)]],
        weightInKg: [
          s?.weightInKg === 0 ? 0 : s?.weightInKg || null,
          [Validators.required],
        ],
      });

      formGroup.valueChanges
        .pipe(
          tap(() => {
            if (loadingCallback) {
              loadingCallback(s.id);
            }
          }),
          debounceTime(1000),
          filter(() => formGroup.valid),
          switchMap((req: WorkoutExerciseSetRecordEditRequest) => {
            return this.editSetRecord(
              workoutRecord.id,
              exerciseRecord.id,
              req.id,
              req,
            ).pipe(map(() => req));
          }),
        )
        .subscribe((req) => {
          if (finishedCallback) {
            finishedCallback(req);
          }
        });

      return formGroup;
    });
  }

  createChangeExerciseInExerciseRecordModal(
    workoutRecordId: string,
    exerciseRecordId: string,
  ) {
    return this.exerciseService.createExerciseSelectionModal().pipe(
      switchMap((exercise: Exercise) => {
        return this.editExerciseRecord(workoutRecordId, exerciseRecordId, {
          exerciseId: exercise.id,
        });
      }),
    );
  }

  showWorkoutRecordModal(workoutRecord: WorkoutRecord, traineeId?: string) {
    this.modalService
      .create(
        {
          component: WorkoutRecordModalComponent,
          componentProps: {
            workoutRecord,
            traineeId,
          },
        },
        'medium',
      )
      .subscribe();
  }

  fetchAndShowWorkoutRecordModal(workoutRecordId: string, traineeId?: string) {
    this.get(workoutRecordId)
      .pipe(catchError(() => EMPTY))
      .subscribe((workoutRecord) => {
        this.showWorkoutRecordModal(workoutRecord, traineeId);
      });
  }

  getSkipExerciseButtons(
    workoutRecord: WorkoutRecord,
    exerciseRecord: WorkoutExerciseRecord,
    callback?: () => void,
  ): Button[] {
    return [
      {
        label: 'buttons.skip',
        handler: () => {
          this.alertService.createConfirmAlert(
            'alerts.confirm.skip-exercise',
            () => {
              this.skipExerciseRecord(
                workoutRecord.id,
                exerciseRecord.id,
              ).subscribe((newExerciseRecord) => {
                this.updateExerciseRecord(workoutRecord, newExerciseRecord);
                if (callback) {
                  callback();
                }
              });
            },
          );
        },
        disabled: () => exerciseRecord.isSkipped,
      },
      {
        label: 'buttons.cancel-skip',
        handler: () => {
          this.alertService.createCancelAlert(() => {
            this.cancelSkipExerciseRecord(
              workoutRecord.id,
              exerciseRecord.id,
            ).subscribe((newExerciseRecord) => {
              this.updateExerciseRecord(workoutRecord, newExerciseRecord);
              if (callback) {
                callback();
              }
            });
          });
        },
        disabled: () => !exerciseRecord.isSkipped,
      },
    ];
  }

  getSetRecordsButtons(
    workoutRecord: WorkoutRecord,
    exerciseRecord: WorkoutExerciseRecord,
    callback?: () => void,
  ) {
    return [
      {
        label: 'training.add-set',
        handler: () => {
          this.alertService.createConfirmAlert('alerts.confirm.add-set', () => {
            this.createSetRecord(workoutRecord.id, exerciseRecord.id).subscribe(
              (setRecord) => {
                exerciseRecord.setRecords =
                  exerciseRecord.setRecords.concat(setRecord);

                if (callback) {
                  callback();
                }
              },
            );
          });
        },
        disabled: () => exerciseRecord.isSkipped,
      },
      {
        label: 'training.remove-set',
        handler: () => {
          const setRecordId =
            exerciseRecord.setRecords[exerciseRecord.setRecords.length - 1].id;

          this.alertService.createConfirmAlert(
            'alerts.confirm.remove-set',
            () => {
              this.deleteSetRecord(
                workoutRecord.id,
                exerciseRecord.id,
                setRecordId,
              ).subscribe(() => {
                exerciseRecord.setRecords = exerciseRecord.setRecords.filter(
                  (s) => s.id !== setRecordId,
                );

                if (callback) {
                  callback();
                }
              });
            },
          );
        },
        disabled: () =>
          exerciseRecord.isSkipped || exerciseRecord.setRecords.length <= 1,
      },
    ];
  }

  getWorkoutRecordNotesButton(
    workoutRecord: WorkoutRecord,
    onUpdate?: (newNotes: Note[]) => void,
  ): Button {
    return {
      label: 'training.workout-notes',
      handler: () => {
        const dateStr = this.dateService.getLocalizedMediumDate(
          workoutRecord.startedOn,
        );

        const workoutStr = this.translateService.instant('training.workout');

        this.modalService.showEntityListModal({
          title: `${workoutStr} ${workoutRecord.workout.orderNumber} - ${dateStr}`,
          entities: workoutRecord.notes,
          entityTextProp: 'content',
          createButtonLabel: 'new-note',
          onCreate: (model: NoteRequest) => {
            return this.createNote(workoutRecord.id, model);
          },
          onEdit: (noteId: string, model: NoteRequest) => {
            return this.noteService.edit(
              environment.TRAINING_SERVICE_API_URL,
              noteId,
              model,
            );
          },
          onDelete: (noteId: string) => {
            return this.noteService.delete(
              environment.TRAINING_SERVICE_API_URL,
              noteId,
            );
          },
          onUpdate,
          onNewPhoto: (noteId: string, event: any): Observable<any> => {
            return this.noteService.onNewPhoto(
              environment.TRAINING_SERVICE_API_URL,
              noteId,
              event,
            );
          },
        });
      },
    };
  }

  getExerciseRecordNotesButton(
    exerciseRecord: WorkoutExerciseRecord,
    onUpdate?: (newNotes: Note[]) => void,
  ): Button {
    return {
      label: 'training.sets-notes',
      handler: () => {
        const dateStr = this.dateService.getLocalizedMediumDate(
          exerciseRecord.workoutRecord.date,
        );

        const exerciseKey = 'exercises.' + exerciseRecord.exercise.exercise.id;
        const exerciseName = this.translateService.instant(exerciseKey);
        const exerciseStr =
          exerciseName !== exerciseKey
            ? exerciseName
            : exerciseRecord.exercise.exercise.name;

        this.modalService.showEntityListModal({
          title: `${dateStr} - ${exerciseStr}`,
          entities: exerciseRecord.notes,
          entityTextProp: 'content',
          createButtonLabel: 'new-note',
          onCreate: (model: NoteRequest) => {
            return this.createExerciseRecordNote(
              exerciseRecord.workoutRecord.id,
              exerciseRecord.id,
              model,
            );
          },
          onEdit: (noteId: string, model: NoteRequest) => {
            return this.noteService.edit(
              environment.TRAINING_SERVICE_API_URL,
              noteId,
              model,
            );
          },
          onDelete: (noteId: string) => {
            return this.noteService.delete(
              environment.TRAINING_SERVICE_API_URL,
              noteId,
            );
          },
          onUpdate,
          onNewPhoto: (noteId: string, event: any): Observable<any> => {
            return this.noteService.onNewPhoto(
              environment.TRAINING_SERVICE_API_URL,
              noteId,
              event,
            );
          },
        });
      },
    };
  }

  getTrainingVolumeButton(workoutRecord: WorkoutRecord): Button {
    return {
      label: 'training.training-volume',
      handler: () => {
        this.exerciseService.showTrainingVolumeModal(
          workoutRecord.trainingVolume,
        );
      },
    };
  }

  getTrainingVolumeHistoryButton(): Button {
    return {
      label: 'training.training-volume-history',
      handler: () => {
        this.modalService
          .create(
            {
              component: TrainingVolumeHistoryModalComponent,
            },
            'medium',
          )
          .subscribe();
      },
    };
  }

  private updateExerciseRecord(
    workoutRecord: WorkoutRecord,
    newExerciseRecord: WorkoutExerciseRecord,
  ) {
    workoutRecord.exerciseRecords = workoutRecord.exerciseRecords.map((er) => {
      if (er.id === newExerciseRecord.id) {
        return newExerciseRecord;
      }

      return er;
    });
  }
}

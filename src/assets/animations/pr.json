{"v": "5.12.1", "fr": 60, "ip": 0, "op": 359, "w": 2160, "h": 3840, "nm": "Alex <PERSON> <PERSON><PERSON>", "ddd": 0, "assets": [{"id": "image_0", "w": 499, "h": 499, "u": "images/", "p": "img_0.png", "e": 0}, {"id": "video_0", "w": 3840, "h": 2160, "u": "images/", "p": "vid_0.mp4", "e": 0}, {"id": "video_1", "w": 3840, "h": 2160, "u": "images/", "p": "vid_1.mp4", "e": 0}], "fonts": {"list": [{"fName": "Bahnschrift-Bold", "fFamily": "Bahnschrift", "fStyle": "Bold", "ascent": 70.99609375}]}, "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "image-removebg-preview (12).png", "cl": "png", "tt": 1, "tp": 7, "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.082, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 101, "s": [-839, 2919, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 162, "s": [936, 2919, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [249.5, 249.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-443.976, 468.075, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 34, "nm": "<PERSON><PERSON><PERSON>", "np": 6, "mn": "ADBE FreePin3", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Puppet Engine", "mn": "ADBE FreePin3 Puppet Engine", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}, {"ty": 0, "nm": "Mesh Rotation Refinement", "mn": "ADBE FreePin3 Auto Rotate Pins", "ix": 2, "v": {"a": 0, "k": 20, "ix": 2}}, {"ty": 7, "nm": "On Transparent", "mn": "ADBE FreePin3 On Transparent", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": "", "nm": "arap", "np": 3, "mn": "ADBE FreePin3 ARAP Group", "ix": 4, "en": 1, "ef": [{"ty": 6, "nm": "Auto-traced Shapes", "mn": "ADBE FreePin3 Outlines", "ix": 1, "v": 0}, {"ty": "", "nm": "<PERSON><PERSON>", "np": 2, "mn": "ADBE FreePin3 Mesh Group", "ix": 2, "en": 1, "ef": [{"ty": "", "nm": "Mesh 1", "np": 8, "mn": "ADBE FreePin3 Mesh Atom", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "<PERSON><PERSON>", "mn": "ADBE FreePin3 Mesh", "ix": 1, "v": 0}, {"ty": 0, "nm": "Triangles", "mn": "ADBE FreePin3 Mesh Tri Count", "ix": 2, "v": {"a": 0, "k": 50, "ix": 2}}, {"ty": 0, "nm": "Density", "mn": "ADBE FreePin3 Mesh Tri Density", "ix": 3, "v": {"a": 0, "k": 10, "ix": 3}}, {"ty": 0, "nm": "Expansion", "mn": "ADBE FreePin3 Mesh Expansion", "ix": 4, "v": {"a": 0, "k": 3, "ix": 4}}, {"ty": "", "nm": "Deform", "np": 11, "mn": "ADBE FreePin3 PosPins", "ix": 5, "en": 1, "ef": [{"ty": "", "nm": "Puppet Pin 4", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 1, "en": 1, "ef": [{"ty": 3, "nm": "Vertex Offset", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [0, 0], "ix": 1}}, {"ty": 0, "nm": "Vertex Index", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 57, "ix": 2}}, {"ty": 7, "nm": "Pin Type", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "Position", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 148, "s": [178.281, 157.421], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 189, "s": [144.045, 157.421], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 231, "s": [111.61, 171.094], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 277, "s": [144.045, 157.421], "to": [0, 0], "ti": [0, 0]}, {"t": 319, "s": [178.281, 157.421]}], "ix": 4}}, {"ty": 0, "nm": "Scale", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "Rotation", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}, {"ty": "", "nm": "Puppet Pin 3", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 2, "en": 1, "ef": [{"ty": 3, "nm": "Vertex Offset", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [0, 0], "ix": 1}}, {"ty": 0, "nm": "Vertex Index", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 58, "ix": 2}}, {"ty": 7, "nm": "Pin Type", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "Position", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 148, "s": [317.027, 92.474], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 231, "s": [221.526, 46.327], "to": [0, 0], "ti": [0, 0]}, {"t": 319, "s": [317.027, 92.474]}], "ix": 4}}, {"ty": 0, "nm": "Scale", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "Rotation", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}, {"ty": "", "nm": "Puppet Pin 2", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 3, "en": 1, "ef": [{"ty": 3, "nm": "Vertex Offset", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [0, 0], "ix": 1}}, {"ty": 0, "nm": "Vertex Index", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 59, "ix": 2}}, {"ty": 7, "nm": "Pin Type", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "Position", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 148, "s": [122.422, 266.805], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 231, "s": [104.403, 294.151], "to": [0, 0], "ti": [0, 0]}, {"t": 319, "s": [122.422, 266.805]}], "ix": 4}}, {"ty": 0, "nm": "Scale", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "Rotation", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}, {"ty": "", "nm": "Puppet Pin 1", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 4, "en": 1, "ef": [{"ty": 3, "nm": "Vertex Offset", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [0, 0], "ix": 1}}, {"ty": 0, "nm": "Vertex Index", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 60, "ix": 2}}, {"ty": 7, "nm": "Pin Type", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "Position", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 0, "k": [353.065, 307.824], "ix": 4}}, {"ty": 0, "nm": "Scale", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "Rotation", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}, {"ty": "", "nm": "Pin 1", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 5, "en": 1, "ef": [{"ty": 3, "nm": "Vertex Offset", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [383.603, 457.518], "ix": 1}}, {"ty": 0, "nm": "Vertex Index", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 4294967295, "ix": 2}}, {"ty": 7, "nm": "Pin Type", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "Position", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 303, "s": [178.281, 157.421], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 334, "s": [144.045, 157.421], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 366, "s": [111.61, 171.094], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 402, "s": [144.045, 157.421], "to": [0, 0], "ti": [0, 0]}, {"t": 434, "s": [178.281, 157.421]}], "ix": 4}}, {"ty": 0, "nm": "Scale", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "Rotation", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}, {"ty": "", "nm": "Pin 2", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 6, "en": 1, "ef": [{"ty": 3, "nm": "Vertex Offset", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [383.603, 457.518], "ix": 1}}, {"ty": 0, "nm": "Vertex Index", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 4294967295, "ix": 2}}, {"ty": 7, "nm": "Pin Type", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "Position", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 303, "s": [317.027, 92.474], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 334, "s": [264.772, 61.709], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 366, "s": [221.526, 46.327], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 402, "s": [264.772, 61.709], "to": [0, 0], "ti": [0, 0]}, {"t": 434, "s": [317.027, 92.474]}], "ix": 4}}, {"ty": 0, "nm": "Scale", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "Rotation", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}, {"ty": "", "nm": "Pin 3", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 7, "en": 1, "ef": [{"ty": 3, "nm": "Vertex Offset", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [383.603, 457.518], "ix": 1}}, {"ty": 0, "nm": "Vertex Index", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 4294967295, "ix": 2}}, {"ty": 7, "nm": "Pin Type", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "Position", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 303, "s": [122.422, 266.805], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 334, "s": [106.205, 280.478], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 366, "s": [104.403, 294.151], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 402, "s": [106.205, 280.478], "to": [0, 0], "ti": [0, 0]}, {"t": 434, "s": [122.422, 266.805]}], "ix": 4}}, {"ty": 0, "nm": "Scale", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "Rotation", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}, {"ty": "", "nm": "Pin 4", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 8, "en": 1, "ef": [{"ty": 3, "nm": "Vertex Offset", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [383.603, 457.518], "ix": 1}}, {"ty": 0, "nm": "Vertex Index", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 4294967295, "ix": 2}}, {"ty": 7, "nm": "Pin Type", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "Position", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 316, "s": [178.281, 157.421], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 347, "s": [144.045, 157.421], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 379, "s": [111.61, 171.094], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 415, "s": [144.045, 157.421], "to": [0, 0], "ti": [0, 0]}, {"t": 447, "s": [178.281, 157.421]}], "ix": 4}}, {"ty": 0, "nm": "Scale", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "Rotation", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}, {"ty": "", "nm": "Pin 5", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 9, "en": 1, "ef": [{"ty": 3, "nm": "Vertex Offset", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [383.603, 457.518], "ix": 1}}, {"ty": 0, "nm": "Vertex Index", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 4294967295, "ix": 2}}, {"ty": 7, "nm": "Pin Type", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "Position", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 316, "s": [317.027, 92.474], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 347, "s": [264.772, 61.709], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 379, "s": [221.526, 46.327], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 415, "s": [264.772, 61.709], "to": [0, 0], "ti": [0, 0]}, {"t": 447, "s": [317.027, 92.474]}], "ix": 4}}, {"ty": 0, "nm": "Scale", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "Rotation", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}, {"ty": "", "nm": "Pin 6", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 10, "en": 1, "ef": [{"ty": 3, "nm": "Vertex Offset", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [383.603, 457.518], "ix": 1}}, {"ty": 0, "nm": "Vertex Index", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 4294967295, "ix": 2}}, {"ty": 7, "nm": "Pin Type", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "Position", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 316, "s": [122.422, 266.805], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 347, "s": [106.205, 280.478], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 379, "s": [104.403, 294.151], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 415, "s": [106.205, 280.478], "to": [0, 0], "ti": [0, 0]}, {"t": 447, "s": [122.422, 266.805]}], "ix": 4}}, {"ty": 0, "nm": "Scale", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "Rotation", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}]}, {"ty": "", "nm": "Overlap", "np": 1, "mn": "ADBE FreePin3 HghtPins", "ix": 6, "en": 1, "ef": []}, {"ty": "", "nm": "<PERSON><PERSON><PERSON>", "np": 1, "mn": "ADBE FreePin3 StarchPins", "ix": 7, "en": 1, "ef": []}]}]}]}]}], "ip": 101, "op": 446, "st": 101, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 9, "nm": "ARROWS.mp4", "cl": "mp4", "tt": 1, "tp": 7, "refId": "video_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"t": 28, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 180, "ix": 10}, "p": {"a": 0, "k": [1795, 3443, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [1920, 1080, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [8, 8, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 20, "nm": "<PERSON><PERSON>", "np": 6, "mn": "ADBE Tint", "ix": 1, "en": 1, "ef": [{"ty": 2, "nm": "Map Black To", "mn": "ADBE Tint-0001", "ix": 1, "v": {"a": 0, "k": [0, 0, 0, 0], "ix": 1}}, {"ty": 2, "nm": "Map White To", "mn": "ADBE Tint-0002", "ix": 2, "v": {"a": 0, "k": [1, 1, 1, 0], "ix": 2}}, {"ty": 0, "nm": "Amount to Tint", "mn": "ADBE Tint-0003", "ix": 3, "v": {"a": 0, "k": 100, "ix": 3}}, {"ty": 6, "nm": "", "mn": "ADBE Tint-0004", "ix": 4, "v": 0}]}], "ip": 0, "op": 360.36036036036, "st": 0, "bm": 2}, {"ddd": 0, "ind": 3, "ty": 5, "nm": "HIT", "tt": 1, "tp": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [799.411, 1868.016, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-41.589, -283.984, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 25, "nm": "Drop Shadow", "np": 8, "mn": "ADBE Drop Shadow", "ix": 1, "en": 1, "ef": [{"ty": 2, "nm": "Shadow Color", "mn": "ADBE Drop Shadow-0001", "ix": 1, "v": {"a": 0, "k": [0, 0, 0, 1], "ix": 1}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Drop Shadow-0002", "ix": 2, "v": {"a": 0, "k": 61.5, "ix": 2}}, {"ty": 0, "nm": "Direction", "mn": "ADBE Drop Shadow-0003", "ix": 3, "v": {"a": 0, "k": 135, "ix": 3}}, {"ty": 0, "nm": "Distance", "mn": "ADBE Drop Shadow-0004", "ix": 4, "v": {"a": 0, "k": 24, "ix": 4}}, {"ty": 0, "nm": "Softness", "mn": "ADBE Drop Shadow-0005", "ix": 5, "v": {"a": 0, "k": 69, "ix": 5}}, {"ty": 7, "nm": "Shadow Only", "mn": "ADBE Drop Shadow-0006", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}, {"ty": 5, "nm": "AC IN [LAW] Controls", "np": 3, "mn": "Pseudo/MHAC PrCtrl LAW 12", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Opacity", "mn": "Pseudo/MHAC PrCtrl LAW 12-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}]}], "t": {"d": {"k": [{"s": {"s": 800, "f": "Bahnschrift-Bold", "t": "HIT", "ca": 1, "j": 2, "tr": 90, "lh": 111, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": [{"nm": "AC IN [LAW] Animator 1", "s": {"t": 0, "xe": {"a": 0, "k": 0, "ix": 7}, "ne": {"a": 0, "k": 0, "ix": 8}, "a": {"a": 0, "k": 100, "ix": 4}, "b": 2, "rn": 0, "sh": 2, "sm": {"a": 0, "k": 100, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 3, "x": "var $bm_rt;\nvar result;\nresult = getAnimationComposerPresetValue();\nfunction getAnimationComposerPresetValue() {\n    var bL = thisLayer;\n    var tTrI = null;\n    var tTrO = null;\n    if (bL.marker.numKeys > 0) {\n        var acmp = 'zzzzzzzzzzzzzzz_AC';\n        for (var i = 1; i <= bL.marker.numKeys; i++) {\n            var m = bL.marker.key(i);\n            var p = m.parameters;\n            if (p.hasOwnProperty(acmp + 'MarkerEnabled') && p.hasOwnProperty(acmp + 'MarkerType') && p.hasOwnProperty(acmp + 'MarkerAppPresetType')) {\n                if (p[acmp + 'MarkerEnabled'] != '1') {\n                    continue;\n                }\n                switch (p[acmp + 'MarkerAppPresetType']) {\n                case '1': {\n                        tTrI = m.time;\n                        break;\n                    }\n                }\n            }\n        }\n    }\n    function acPD(fxN) {\n        try {\n            if (bL.effect(fxN).active === false) {\n                ac.en = false;\n            }\n        } catch (e) {\n        }\n    }\n    function acValAdd(val) {\n        acAccA = $bm_sum(acAccA, val);\n    }\n    function upACo(ac, tIn, dIn, tOut, dOut, eFi, eFo) {\n        if (dIn < 0) {\n            dIn = 0;\n        }\n        if (dOut < 0) {\n            dOut = 0;\n        }\n        ac.fade = 1;\n        if (t < tOut) {\n            if (t < $bm_sum(tIn, dIn)) {\n                if (dIn == 0) {\n                    ac.fade = 0;\n                } else {\n                    ac.fade = $bm_div($bm_sub(t, tIn), dIn);\n                    if (ac.fade < 0) {\n                        ac.fade = 0;\n                    }\n                    if (ac.fade > 1) {\n                        ac.fade = 1;\n                    }\n                    if (eFi) {\n                        ac.fade = eFi(ac.fade);\n                    }\n                }\n            }\n        } else {\n            if (dOut == 0) {\n                ac.fade = 0;\n            } else {\n                ac.fade = $bm_div($bm_sub(t, tOut), dOut);\n                if (ac.fade < 0) {\n                    ac.fade = 0;\n                }\n                if (ac.fade > 1) {\n                    ac.fade = 1;\n                }\n                if (eFo) {\n                    ac.fade = eFo(ac.fade);\n                }\n                ac.fade = $bm_sub(1, ac.fade);\n            }\n        }\n        ac.intensity = 1;\n        ac.en = true;\n        ac.t = $bm_sub(t, tIn);\n    }\n    var ac = {\n        'en': false,\n        'fade': 0\n    };\n    var acI = {\n        'en': false,\n        'fade': 0\n    };\n    var acO = {\n        'en': false,\n        'fade': 0\n    };\n    ac.en = false;\n    var v = thisProperty.value;\n    var t = time;\n    var acAccA = 0;\n    if (tTrI !== null && t < tTrI) {\n        upACo(acI, bL.inPoint, $bm_sub(tTrI, bL.inPoint), bL.outPoint, 0);\n        acI.fade = $bm_sub(1, acI.fade);\n    }\n    ac = acI;\n    acPD('AC IN [LAW] Controls');\n    (function () {\n        if (ac.en) {\n            acValAdd($bm_mul(-200, ac.fade));\n        }\n    }());\n    v = thisProperty.value;\n    v = $bm_sum(v, acAccA);\n    return v;\n}\n$bm_rt = result;"}, "r": 1}, "a": {"o": {"a": 0, "k": 0, "ix": 9, "x": "var $bm_rt;\nvar result;\nresult = getAnimationComposerPresetValue();\nfunction getAnimationComposerPresetValue() {\n    var bL = thisLayer;\n    var v = thisProperty.value;\n    function acValSet(val) {\n        v = val;\n    }\n    function acSliderValP(fxN, stN, dV) {\n        try {\n            return bL.effect(fxN)(stN);\n        } catch (e) {\n            return dV;\n        }\n    }\n    (function () {\n        acValSet(acSliderValP('AC IN [LAW] Controls', 'Opacity', 0));\n    }());\n    return v;\n}\n$bm_rt = result;"}}}]}, "ip": 78, "op": 395, "st": 78, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 5, "nm": "PRs", "tt": 1, "tp": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [873.411, 1265.016, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-41.589, -283.984, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 25, "nm": "Drop Shadow", "np": 8, "mn": "ADBE Drop Shadow", "ix": 1, "en": 1, "ef": [{"ty": 2, "nm": "Shadow Color", "mn": "ADBE Drop Shadow-0001", "ix": 1, "v": {"a": 0, "k": [0, 0, 0, 1], "ix": 1}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Drop Shadow-0002", "ix": 2, "v": {"a": 0, "k": 61.5, "ix": 2}}, {"ty": 0, "nm": "Direction", "mn": "ADBE Drop Shadow-0003", "ix": 3, "v": {"a": 0, "k": 135, "ix": 3}}, {"ty": 0, "nm": "Distance", "mn": "ADBE Drop Shadow-0004", "ix": 4, "v": {"a": 0, "k": 24, "ix": 4}}, {"ty": 0, "nm": "Softness", "mn": "ADBE Drop Shadow-0005", "ix": 5, "v": {"a": 0, "k": 69, "ix": 5}}, {"ty": 7, "nm": "Shadow Only", "mn": "ADBE Drop Shadow-0006", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}, {"ty": 5, "nm": "AC IN [LAW] Controls", "np": 3, "mn": "Pseudo/MHAC PrCtrl LAW 12", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Opacity", "mn": "Pseudo/MHAC PrCtrl LAW 12-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}]}], "t": {"d": {"k": [{"s": {"s": 800, "f": "Bahnschrift-Bold", "t": "PRS", "ca": 1, "j": 2, "tr": -82, "lh": 111, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": [{"nm": "AC IN [LAW] Animator 1", "s": {"t": 0, "xe": {"a": 0, "k": 0, "ix": 7}, "ne": {"a": 0, "k": 0, "ix": 8}, "a": {"a": 0, "k": 100, "ix": 4}, "b": 2, "rn": 0, "sh": 2, "sm": {"a": 0, "k": 100, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 3, "x": "var $bm_rt;\nvar result;\nresult = getAnimationComposerPresetValue();\nfunction getAnimationComposerPresetValue() {\n    var bL = thisLayer;\n    var tTrI = null;\n    var tTrO = null;\n    if (bL.marker.numKeys > 0) {\n        var acmp = 'zzzzzzzzzzzzzzz_AC';\n        for (var i = 1; i <= bL.marker.numKeys; i++) {\n            var m = bL.marker.key(i);\n            var p = m.parameters;\n            if (p.hasOwnProperty(acmp + 'MarkerEnabled') && p.hasOwnProperty(acmp + 'MarkerType') && p.hasOwnProperty(acmp + 'MarkerAppPresetType')) {\n                if (p[acmp + 'MarkerEnabled'] != '1') {\n                    continue;\n                }\n                switch (p[acmp + 'MarkerAppPresetType']) {\n                case '1': {\n                        tTrI = m.time;\n                        break;\n                    }\n                }\n            }\n        }\n    }\n    function acPD(fxN) {\n        try {\n            if (bL.effect(fxN).active === false) {\n                ac.en = false;\n            }\n        } catch (e) {\n        }\n    }\n    function acValAdd(val) {\n        acAccA = $bm_sum(acAccA, val);\n    }\n    function upACo(ac, tIn, dIn, tOut, dOut, eFi, eFo) {\n        if (dIn < 0) {\n            dIn = 0;\n        }\n        if (dOut < 0) {\n            dOut = 0;\n        }\n        ac.fade = 1;\n        if (t < tOut) {\n            if (t < $bm_sum(tIn, dIn)) {\n                if (dIn == 0) {\n                    ac.fade = 0;\n                } else {\n                    ac.fade = $bm_div($bm_sub(t, tIn), dIn);\n                    if (ac.fade < 0) {\n                        ac.fade = 0;\n                    }\n                    if (ac.fade > 1) {\n                        ac.fade = 1;\n                    }\n                    if (eFi) {\n                        ac.fade = eFi(ac.fade);\n                    }\n                }\n            }\n        } else {\n            if (dOut == 0) {\n                ac.fade = 0;\n            } else {\n                ac.fade = $bm_div($bm_sub(t, tOut), dOut);\n                if (ac.fade < 0) {\n                    ac.fade = 0;\n                }\n                if (ac.fade > 1) {\n                    ac.fade = 1;\n                }\n                if (eFo) {\n                    ac.fade = eFo(ac.fade);\n                }\n                ac.fade = $bm_sub(1, ac.fade);\n            }\n        }\n        ac.intensity = 1;\n        ac.en = true;\n        ac.t = $bm_sub(t, tIn);\n    }\n    var ac = {\n        'en': false,\n        'fade': 0\n    };\n    var acI = {\n        'en': false,\n        'fade': 0\n    };\n    var acO = {\n        'en': false,\n        'fade': 0\n    };\n    ac.en = false;\n    var v = thisProperty.value;\n    var t = time;\n    var acAccA = 0;\n    if (tTrI !== null && t < tTrI) {\n        upACo(acI, bL.inPoint, $bm_sub(tTrI, bL.inPoint), bL.outPoint, 0);\n        acI.fade = $bm_sub(1, acI.fade);\n    }\n    ac = acI;\n    acPD('AC IN [LAW] Controls');\n    (function () {\n        if (ac.en) {\n            acValAdd($bm_mul(-200, ac.fade));\n        }\n    }());\n    v = thisProperty.value;\n    v = $bm_sum(v, acAccA);\n    return v;\n}\n$bm_rt = result;"}, "r": 1}, "a": {"o": {"a": 0, "k": 0, "ix": 9, "x": "var $bm_rt;\nvar result;\nresult = getAnimationComposerPresetValue();\nfunction getAnimationComposerPresetValue() {\n    var bL = thisLayer;\n    var v = thisProperty.value;\n    function acValSet(val) {\n        v = val;\n    }\n    function acSliderValP(fxN, stN, dV) {\n        try {\n            return bL.effect(fxN)(stN);\n        } catch (e) {\n            return dV;\n        }\n    }\n    (function () {\n        acValSet(acSliderValP('AC IN [LAW] Controls', 'Opacity', 0));\n    }());\n    return v;\n}\n$bm_rt = result;"}}}]}, "ip": 56, "op": 395, "st": 56, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 5, "nm": "18", "tt": 1, "tp": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [582.411, 668.016, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-41.589, -283.984, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 25, "nm": "Drop Shadow", "np": 8, "mn": "ADBE Drop Shadow", "ix": 1, "en": 1, "ef": [{"ty": 2, "nm": "Shadow Color", "mn": "ADBE Drop Shadow-0001", "ix": 1, "v": {"a": 0, "k": [0, 0, 0, 1], "ix": 1}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Drop Shadow-0002", "ix": 2, "v": {"a": 0, "k": 61.5, "ix": 2}}, {"ty": 0, "nm": "Direction", "mn": "ADBE Drop Shadow-0003", "ix": 3, "v": {"a": 0, "k": 135, "ix": 3}}, {"ty": 0, "nm": "Distance", "mn": "ADBE Drop Shadow-0004", "ix": 4, "v": {"a": 0, "k": 24, "ix": 4}}, {"ty": 0, "nm": "Softness", "mn": "ADBE Drop Shadow-0005", "ix": 5, "v": {"a": 0, "k": 69, "ix": 5}}, {"ty": 7, "nm": "Shadow Only", "mn": "ADBE Drop Shadow-0006", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}, {"ty": 5, "nm": "AC IN [LAW] Controls", "np": 3, "mn": "Pseudo/MHAC PrCtrl LAW 12", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Opacity", "mn": "Pseudo/MHAC PrCtrl LAW 12-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}]}], "t": {"d": {"k": [{"s": {"s": 800, "f": "Bahnschrift-Bold", "t": "18", "ca": 1, "j": 2, "tr": -82, "lh": 111, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": [{"nm": "AC IN [LAW] Animator 1", "s": {"t": 0, "xe": {"a": 0, "k": 0, "ix": 7}, "ne": {"a": 0, "k": 0, "ix": 8}, "a": {"a": 0, "k": 100, "ix": 4}, "b": 2, "rn": 0, "sh": 2, "sm": {"a": 0, "k": 100, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 3, "x": "var $bm_rt;\nvar result;\nresult = getAnimationComposerPresetValue();\nfunction getAnimationComposerPresetValue() {\n    var bL = thisLayer;\n    var tTrI = null;\n    var tTrO = null;\n    if (bL.marker.numKeys > 0) {\n        var acmp = 'zzzzzzzzzzzzzzz_AC';\n        for (var i = 1; i <= bL.marker.numKeys; i++) {\n            var m = bL.marker.key(i);\n            var p = m.parameters;\n            if (p.hasOwnProperty(acmp + 'MarkerEnabled') && p.hasOwnProperty(acmp + 'MarkerType') && p.hasOwnProperty(acmp + 'MarkerAppPresetType')) {\n                if (p[acmp + 'MarkerEnabled'] != '1') {\n                    continue;\n                }\n                switch (p[acmp + 'MarkerAppPresetType']) {\n                case '1': {\n                        tTrI = m.time;\n                        break;\n                    }\n                }\n            }\n        }\n    }\n    function acPD(fxN) {\n        try {\n            if (bL.effect(fxN).active === false) {\n                ac.en = false;\n            }\n        } catch (e) {\n        }\n    }\n    function acValAdd(val) {\n        acAccA = $bm_sum(acAccA, val);\n    }\n    function upACo(ac, tIn, dIn, tOut, dOut, eFi, eFo) {\n        if (dIn < 0) {\n            dIn = 0;\n        }\n        if (dOut < 0) {\n            dOut = 0;\n        }\n        ac.fade = 1;\n        if (t < tOut) {\n            if (t < $bm_sum(tIn, dIn)) {\n                if (dIn == 0) {\n                    ac.fade = 0;\n                } else {\n                    ac.fade = $bm_div($bm_sub(t, tIn), dIn);\n                    if (ac.fade < 0) {\n                        ac.fade = 0;\n                    }\n                    if (ac.fade > 1) {\n                        ac.fade = 1;\n                    }\n                    if (eFi) {\n                        ac.fade = eFi(ac.fade);\n                    }\n                }\n            }\n        } else {\n            if (dOut == 0) {\n                ac.fade = 0;\n            } else {\n                ac.fade = $bm_div($bm_sub(t, tOut), dOut);\n                if (ac.fade < 0) {\n                    ac.fade = 0;\n                }\n                if (ac.fade > 1) {\n                    ac.fade = 1;\n                }\n                if (eFo) {\n                    ac.fade = eFo(ac.fade);\n                }\n                ac.fade = $bm_sub(1, ac.fade);\n            }\n        }\n        ac.intensity = 1;\n        ac.en = true;\n        ac.t = $bm_sub(t, tIn);\n    }\n    var ac = {\n        'en': false,\n        'fade': 0\n    };\n    var acI = {\n        'en': false,\n        'fade': 0\n    };\n    var acO = {\n        'en': false,\n        'fade': 0\n    };\n    ac.en = false;\n    var v = thisProperty.value;\n    var t = time;\n    var acAccA = 0;\n    if (tTrI !== null && t < tTrI) {\n        upACo(acI, bL.inPoint, $bm_sub(tTrI, bL.inPoint), bL.outPoint, 0);\n        acI.fade = $bm_sub(1, acI.fade);\n    }\n    ac = acI;\n    acPD('AC IN [LAW] Controls');\n    (function () {\n        if (ac.en) {\n            acValAdd($bm_mul(-200, ac.fade));\n        }\n    }());\n    v = thisProperty.value;\n    v = $bm_sum(v, acAccA);\n    return v;\n}\n$bm_rt = result;"}, "r": 1}, "a": {"o": {"a": 0, "k": 0, "ix": 9, "x": "var $bm_rt;\nvar result;\nresult = getAnimationComposerPresetValue();\nfunction getAnimationComposerPresetValue() {\n    var bL = thisLayer;\n    var v = thisProperty.value;\n    function acValSet(val) {\n        v = val;\n    }\n    function acSliderValP(fxN, stN, dV) {\n        try {\n            return bL.effect(fxN)(stN);\n        } catch (e) {\n            return dV;\n        }\n    }\n    (function () {\n        acValSet(acSliderValP('AC IN [LAW] Controls', 'Opacity', 0));\n    }());\n    return v;\n}\n$bm_rt = result;"}}}]}, "ip": 29, "op": 395, "st": 29, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 9, "nm": "LINES_06.mp4", "cl": "mp4", "tt": 3, "tp": 7, "refId": "video_1", "sr": 1, "ks": {"o": {"a": 0, "k": 18, "ix": 11}, "r": {"a": 0, "k": 90, "ix": 10}, "p": {"a": 0, "k": [1080, 1920, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [1920, 1080, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Stereo Mixer", "np": 7, "mn": "ADBE Aud Stereo Mixer", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Left Level", "mn": "ADBE Aud Stereo Mixer-0001", "ix": 1, "v": {"a": 0, "k": 100, "ix": 1}}, {"ty": 0, "nm": "Right Level", "mn": "ADBE Aud Stereo Mixer-0002", "ix": 2, "v": {"a": 0, "k": 100, "ix": 2}}, {"ty": 0, "nm": "Left Pan", "mn": "ADBE Aud Stereo Mixer-0003", "ix": 3, "v": {"a": 0, "k": -100, "ix": 3}}, {"ty": 0, "nm": "Right Pan", "mn": "ADBE Aud Stereo Mixer-0004", "ix": 4, "v": {"a": 0, "k": 100, "ix": 4}}, {"ty": 7, "nm": "Invert Phase", "mn": "ADBE Aud Stereo Mixer-0005", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}]}], "ip": 0, "op": 366, "st": 0, "bm": 2}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Shape Layer 1", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1083.5, 1899.25, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.097, 0.097, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 72, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 345, "s": [100, 100, 100]}, {"t": 358, "s": [0, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "4-Color Gradient", "np": 16, "mn": "ADBE 4ColorGradient", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "Positions & Colors", "mn": "ADBE 4ColorGradient-0011", "ix": 1, "v": 0}, {"ty": 3, "nm": "Point 1", "mn": "ADBE 4ColorGradient-0001", "ix": 2, "v": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [2032, 1912], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 75, "s": [233.194, 3586.49], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 121, "s": [172, 1959], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 176, "s": [2089, 1923], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 241, "s": [2032, 1912], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 316, "s": [233.194, 3586.49], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 362, "s": [172, 1959], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 417, "s": [2089, 1923], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 480, "s": [233.194, 3586.49], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 526, "s": [172, 1959], "to": [0, 0], "ti": [0, 0]}, {"t": 601, "s": [2089, 1923]}], "ix": 2}}, {"ty": 2, "nm": "Color 1", "mn": "ADBE 4ColorGradient-0002", "ix": 3, "v": {"a": 0, "k": [0.568688750267, 0, 0.03810800612, 1], "ix": 3}}, {"ty": 3, "nm": "Point 2", "mn": "ADBE 4ColorGradient-0003", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 0, "s": [1080, 1912], "to": [0, 0], "ti": [0, 0]}, {"t": 241, "s": [1080, 1912]}], "ix": 4}}, {"ty": 2, "nm": "Color 2", "mn": "ADBE 4ColorGradient-0004", "ix": 5, "v": {"a": 0, "k": [1, 0.149019613862, 0.180392161012, 1], "ix": 5}}, {"ty": 3, "nm": "Point 3", "mn": "ADBE 4ColorGradient-0005", "ix": 6, "v": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [216, 240], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 75, "s": [2007.6, 3653.9], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 121, "s": [1956, 3611], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 176, "s": [-60, 3287], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 241, "s": [216, 240], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 316, "s": [2007.6, 3653.9], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 362, "s": [1956, 3611], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 417, "s": [-60, 3287], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 480, "s": [2007.6, 3653.9], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 526, "s": [1956, 3611], "to": [0, 0], "ti": [0, 0]}, {"t": 601, "s": [-60, 3287]}], "ix": 6}}, {"ty": 2, "nm": "Color 3", "mn": "ADBE 4ColorGradient-0006", "ix": 7, "v": {"a": 0, "k": [0.568627476692, 0, 0.039215687662, 1], "ix": 7}}, {"ty": 3, "nm": "Point 4", "mn": "ADBE 4ColorGradient-0007", "ix": 8, "v": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [184, 3648], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 75, "s": [1087.001, 172.399], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 121, "s": [1948, 230], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 176, "s": [-95, 833], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 241, "s": [184, 3648], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 316, "s": [1087.001, 172.399], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 362, "s": [1948, 230], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 417, "s": [-95, 833], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 480, "s": [1087.001, 172.399], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 526, "s": [1948, 230], "to": [0, 0], "ti": [0, 0]}, {"t": 601, "s": [-95, 833]}], "ix": 8}}, {"ty": 2, "nm": "Color 4", "mn": "ADBE 4ColorGradient-0008", "ix": 9, "v": {"a": 0, "k": [0.529687523842, 0, 0, 1], "ix": 9}}, {"ty": 6, "nm": "Color 4", "mn": "ADBE 4ColorGradient-0012", "ix": 10, "v": 0}, {"ty": 0, "nm": "Blend", "mn": "ADBE 4ColorGradient-0009", "ix": 11, "v": {"a": 0, "k": 500, "ix": 11}}, {"ty": 0, "nm": "Jitter", "mn": "ADBE 4ColorGradient-0010", "ix": 12, "v": {"a": 0, "k": 0, "ix": 12}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE 4ColorGradient-0013", "ix": 13, "v": {"a": 0, "k": 100, "ix": 13}}, {"ty": 7, "nm": "Blending Mode", "mn": "ADBE 4ColorGradient-0014", "ix": 14, "v": {"a": 0, "k": 1, "ix": 14}}]}, {"ty": 5, "nm": "Noise", "np": 5, "mn": "ADBE Noise2", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Amount of Noise", "mn": "ADBE Noise2-0001", "ix": 1, "v": {"a": 0, "k": 10, "ix": 1}}, {"ty": 7, "nm": "Noise Type", "mn": "ADBE Noise2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 7, "nm": "Clipping", "mn": "ADBE Noise2-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [1835, 3503], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 222, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.958, 0.501, 0.527, 0.51, 0.935, 0.313, 0.341, 1, 0.913, 0.126, 0.155], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [511.363, -2342.426], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-2.5, -8.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 366, "st": 0, "ct": 1, "bm": 0}], "markers": [], "props": {}, "chars": [{"ch": "1", "size": 800, "style": "Bold", "w": 33.25, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[25.439, -70.996], [14.014, -70.996], [3.418, -66.211], [3.418, -52.002], [12.061, -55.273], [12.061, 0], [25.439, 0]], "c": true}, "ix": 2}, "nm": "1", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Bahnschrift"}, {"ch": "8", "size": 800, "style": "Bold", "w": 56.25, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-4.492, 0], [-3.402, 1.628], [-1.855, 2.979], [0, 3.939], [0, 0], [1.985, 3.109], [3.288, 1.628], [-1.726, 2.523], [0, 2.93], [0, 0], [1.741, 2.816], [3.174, 1.546], [4.199, 0], [3.174, -1.546], [1.741, -2.815], [0, -3.711], [0, 0], [-1.693, -2.539], [-2.8, -1.367], [1.953, -3.125], [0, -3.548], [0, 0], [-1.855, -2.979], [-3.402, -1.627]], "o": [[4.492, 0], [3.401, -1.627], [1.855, -2.979], [0, 0], [0, -3.548], [-1.986, -3.108], [2.832, -1.334], [1.725, -2.522], [0, 0], [0, -3.711], [-1.742, -2.815], [-3.174, -1.546], [-4.199, 0], [-3.174, 1.546], [-1.742, 2.816], [0, 0], [0, 2.865], [1.692, 2.539], [-3.255, 1.595], [-1.953, 3.125], [0, 0], [0, 3.939], [1.855, 2.979], [3.401, 1.628]], "v": [[28.125, 0.732], [39.966, -1.709], [47.852, -8.618], [50.635, -18.994], [50.635, -20.166], [47.656, -30.151], [39.746, -37.256], [46.582, -43.042], [49.17, -51.221], [49.17, -53.076], [46.558, -62.866], [39.185, -69.409], [28.125, -71.729], [17.065, -69.409], [9.692, -62.866], [7.08, -53.076], [7.08, -51.221], [9.619, -43.115], [16.357, -37.256], [8.545, -30.176], [5.615, -20.166], [5.615, -18.994], [8.398, -8.618], [16.284, -1.709]], "c": true}, "ix": 2}, "nm": "8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[1.79, 0], [1.383, 0.716], [0.764, 1.318], [0, 1.726], [0, 0], [-0.765, 1.221], [-1.367, 0.668], [-1.823, 0], [-1.367, -0.667], [-0.765, -1.204], [0, -1.595], [0, 0], [0.764, -1.334], [1.383, -0.732]], "o": [[-1.791, 0], [-1.384, -0.716], [-0.765, -1.318], [0, 0], [0, -1.595], [0.764, -1.221], [1.367, -0.667], [1.823, 0], [1.367, 0.668], [0.764, 1.205], [0, 0], [0, 1.726], [-0.765, 1.335], [-1.384, 0.732]], "v": [[28.125, -12.158], [23.364, -13.232], [20.142, -16.284], [18.994, -20.85], [18.994, -21.582], [20.142, -25.806], [23.34, -28.638], [28.125, -29.639], [32.91, -28.638], [36.108, -25.83], [37.256, -21.631], [37.256, -20.947], [36.108, -16.357], [32.886, -13.257]], "c": true}, "ix": 2}, "nm": "8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[1.53, 0], [1.155, 0.668], [0.635, 1.205], [0, 1.562], [0, 0], [-0.635, 1.14], [-1.156, 0.619], [-1.53, 0], [-1.156, -0.618], [-0.635, -1.123], [0, -1.497], [0, 0], [0.635, -1.221], [1.155, -0.667]], "o": [[-1.53, 0], [-1.156, -0.667], [-0.635, -1.204], [0, 0], [0, -1.497], [0.635, -1.139], [1.155, -0.618], [1.53, 0], [1.155, 0.619], [0.635, 1.123], [0, 0], [0, 1.595], [-0.635, 1.221], [-1.156, 0.668]], "v": [[28.125, -42.627], [24.097, -43.628], [21.411, -46.436], [20.459, -50.586], [20.459, -51.318], [21.411, -55.273], [24.097, -57.91], [28.125, -58.838], [32.153, -57.91], [34.839, -55.298], [35.791, -51.367], [35.791, -50.684], [34.839, -46.46], [32.153, -43.628]], "c": true}, "ix": 2}, "nm": "8", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "8", "np": 6, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Bahnschrift"}, {"ch": "P", "size": 800, "style": "Bold", "w": 62.26, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-3.369, 1.791], [-1.855, 3.271], [0, 4.33], [1.855, 3.288], [3.369, 1.807], [4.427, 0], [0, 0], [0, 0], [0, 0], [-1.221, -0.732], [-0.668, -1.35], [0, -1.758], [0.684, -1.334], [1.221, -0.732], [1.595, 0], [0, 0]], "o": [[0, 0], [4.427, 0], [3.369, -1.79], [1.855, -3.271], [0, -4.329], [-1.855, -3.288], [-3.369, -1.807], [0, 0], [0, 0], [0, 0], [1.627, 0], [1.221, 0.732], [0.667, 1.351], [0, 1.726], [-0.684, 1.335], [-1.221, 0.732], [0, 0], [0, 0]], "v": [[13.916, -27.539], [36.279, -27.539], [47.974, -30.225], [55.811, -37.817], [58.594, -49.219], [55.811, -60.645], [47.974, -68.286], [36.279, -70.996], [13.916, -70.996], [13.916, -58.105], [36.621, -58.105], [40.894, -57.007], [43.726, -53.882], [44.727, -49.219], [43.701, -44.629], [40.845, -41.528], [36.621, -40.43], [13.916, -40.43]], "c": true}, "ix": 2}, "nm": "P", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[7.812, 0], [21.191, 0], [21.191, -70.996], [7.812, -70.996]], "c": true}, "ix": 2}, "nm": "P", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "P", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Bahnschrift"}, {"ch": "R", "size": 800, "style": "Bold", "w": 65.19, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-2.979, 1.758], [-1.628, 3.207], [0, 4.232], [1.627, 3.207], [2.962, 1.758], [3.906, 0], [0, 0], [0, 0], [0, 0], [-1.042, -0.684], [-0.57, -1.27], [0, -1.66], [0.569, -1.27], [1.041, -0.684], [1.367, 0], [0, 0]], "o": [[0, 0], [3.873, 0], [2.979, -1.758], [1.627, -3.206], [0, -4.231], [-1.628, -3.206], [-2.962, -1.758], [0, 0], [0, 0], [0, 0], [1.399, 0], [1.041, 0.684], [0.569, 1.27], [-0.033, 1.66], [-0.57, 1.27], [-1.042, 0.684], [0, 0], [0, 0]], "v": [[13.135, -28.564], [38.916, -28.564], [49.194, -31.201], [56.104, -38.647], [58.545, -49.805], [56.104, -60.962], [49.219, -68.408], [38.916, -71.045], [13.135, -71.045], [13.135, -58.154], [37.842, -58.154], [41.504, -57.129], [43.921, -54.199], [44.775, -49.805], [43.872, -45.41], [41.455, -42.48], [37.842, -41.455], [13.135, -41.455]], "c": true}, "ix": 2}, "nm": "R", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[7.812, 0], [21.191, 0], [21.191, -71.045], [7.812, -71.045]], "c": true}, "ix": 2}, "nm": "R", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[45.312, 0], [61.523, 0], [42.383, -33.008], [28.076, -30.762]], "c": true}, "ix": 2}, "nm": "R", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "R", "np": 6, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Bahnschrift"}, {"ch": "S", "size": 800, "style": "Bold", "w": 61.57, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-3.386, 0], [-4.053, 1.693], [-2.116, 3.223], [0, 4.525], [0, 0], [2.018, 2.783], [3.011, 1.254], [4.492, 0.879], [0.179, 0.049], [0.195, 0.033], [0.13, 0.017], [0.13, 0.033], [1.725, 0.554], [1.172, 1.189], [0, 2.051], [0, 0], [-2.246, 1.351], [-4.102, 0], [-2.979, -1.041], [-2.734, -1.888], [0, 0], [2.539, 1.107], [2.653, 0.586], [2.539, 0], [3.906, -1.709], [2.051, -3.255], [0, -4.59], [0, 0], [-2.181, -2.815], [-3.125, -1.123], [-4.753, -0.748], [0, 0], [-0.082, 0], [-0.098, -0.032], [-1.726, -0.52], [-1.074, -1.106], [0, -1.888], [0, 0], [2.409, -1.481], [4.362, 0], [3.58, 1.221], [2.734, 2.246], [0, 0], [-2.849, -1.318], [-3.239, -0.684]], "o": [[5.696, 0], [4.053, -1.692], [2.116, -3.223], [0, 0], [0, -4.817], [-2.019, -2.783], [-3.011, -1.253], [-0.195, -0.032], [-0.179, -0.049], [-0.13, -0.032], [-0.13, -0.016], [-3.125, -0.618], [-1.726, -0.553], [-1.172, -1.188], [0, 0], [0, -2.441], [2.246, -1.35], [2.734, 0], [2.979, 1.042], [0, 0], [-2.279, -1.562], [-2.539, -1.106], [-2.654, -0.586], [-5.534, 0], [-3.906, 1.709], [-2.051, 3.255], [0, 0], [0, 5.176], [2.18, 2.816], [3.125, 1.123], [0, 0], [0.098, 0.033], [0.081, 0], [2.702, 0.423], [1.725, 0.521], [1.074, 1.107], [0, 0], [0, 2.702], [-2.409, 1.482], [-3.777, 0], [-3.581, -1.221], [0, 0], [2.246, 1.855], [2.848, 1.318], [3.239, 0.684]], "v": [[30.127, 0.732], [44.751, -1.807], [54.004, -9.18], [57.178, -20.801], [57.178, -20.898], [54.15, -32.3], [46.606, -38.354], [35.352, -41.553], [34.79, -41.675], [34.229, -41.797], [33.838, -41.87], [33.447, -41.943], [26.172, -43.701], [21.826, -46.313], [20.068, -51.172], [20.068, -51.221], [23.438, -56.909], [32.959, -58.936], [41.528, -57.373], [50.098, -52.979], [55.762, -64.307], [48.535, -68.311], [40.747, -70.85], [32.959, -71.729], [18.799, -69.165], [9.863, -61.719], [6.787, -49.951], [6.787, -49.902], [10.059, -37.915], [18.018, -32.007], [29.834, -29.199], [30.908, -29.053], [31.177, -29.004], [31.445, -28.955], [38.086, -27.539], [42.285, -25.098], [43.896, -20.605], [43.896, -20.557], [40.283, -14.282], [30.127, -12.061], [19.092, -13.892], [9.619, -19.092], [3.418, -8.057], [11.06, -3.296], [20.19, -0.293]], "c": true}, "ix": 2}, "nm": "S", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "S", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Bahnschrift"}, {"ch": "H", "size": 800, "style": "Bold", "w": 67.87, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[46.68, 0], [60.059, 0], [60.059, -70.996], [46.68, -70.996]], "c": true}, "ix": 2}, "nm": "H", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[7.812, 0], [21.191, 0], [21.191, -70.996], [7.812, -70.996]], "c": true}, "ix": 2}, "nm": "H", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[14.014, -28.76], [55.371, -28.76], [55.371, -41.65], [14.014, -41.65]], "c": true}, "ix": 2}, "nm": "H", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "H", "np": 6, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Bahnschrift"}, {"ch": "I", "size": 800, "style": "Bold", "w": 27.54, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[20.703, -70.996], [6.836, -70.996], [6.836, 0], [20.703, 0]], "c": true}, "ix": 2}, "nm": "I", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "I", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Bahnschrift"}, {"ch": "T", "size": 800, "style": "Bold", "w": 48.14, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[17.383, 0], [30.762, 0], [30.762, -65.186], [17.383, -65.186]], "c": true}, "ix": 2}, "nm": "T", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.953, -58.105], [50.098, -58.105], [50.098, -70.996], [-1.953, -70.996]], "c": true}, "ix": 2}, "nm": "T", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "T", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Bahnschrift"}]}
<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1080" height="900" viewBox="0 0 1080 900" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<g transform="matrix(1 0 0 1 540 450)" id="591adff2-6817-475a-8317-f74086b814f2"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-450" rx="0" ry="0" width="1080" height="900" />
</g>
<g transform="matrix(0 0 0 0 0 0)" id="8e4adf92-820d-4062-b661-b838134f425f"  >
</g>
<g transform="matrix(18.03 0 0 18.03 539.8 98.63)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  cx="0" cy="0" r="5" />
</g>
<g transform="matrix(18.03 0 0 18.03 539.97 340.48)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-32, -26.42)" d="M 61 40 C 60.99570134546484 36.13578871279056 57.86421128720944 33.00429865453516 54 33 L 53 33 L 53 30 C 52.99448887104065 25.03172182642345 48.96827817357654 21.005511128959345 44 21 L 42.256 21 L 47.426 13.9443 C 48.23594588784516 12.838268698057536 48.04085721226659 11.292172290354323 46.98155183679921 10.422005809949704 C 45.922246461331845 9.551839329545086 44.367696989559356 9.660695349423015 43.44 10.67 L 34.8691 20 L 29.1309 20 L 20.56 10.67 C 19.63228543096015 9.660147579490115 18.07718840883259 9.551129214757685 17.017651117287784 10.421667531343672 C 15.958113825742979 11.292205847929658 15.763394151172848 12.83891073264886 16.574099999999998 13.9449 L 21.7438 21 L 20 21 C 15.031721826423452 21.005511128959345 11.005511128959345 25.031721826423457 11 30.000000000000004 L 11 33 L 10 33 C 6.135788712790557 33.00429865453516 3.0042986545351624 36.13578871279056 3 40 L 3 43 L 61 43 Z M 13 33 L 13 30 C 13.004298654535162 26.135788712790557 16.135788712790557 23.004298654535162 20 23 L 23.21 23 L 24.6885 25.0176 L 24.3047 30.0059 C 21.761159334783272 30.679876760149142 19.317694568514973 31.68660266961563 17.037599999999998 33 Z M 39.6956 30.0059 L 39.312 25.0176 L 40.7905 23 L 44 23 C 47.86421128720944 23.004298654535162 50.99570134546484 26.135788712790557 51 30 L 51 33 L 46.9624 33 C 44.68240419495406 31.68661749808569 42.23904052082859 30.679891679747623 39.6956 30.0059 Z" stroke-linecap="round" />
</g>
<g transform="matrix(18.03 0 0 18.03 540 783.34)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-32, -51)" d="M 2.9905 45 L 3.0127 52.0911 C 3.013547336189032 52.35631691046761 3.1197176755373137 52.61033447184611 3.3078544656971465 52.79727102729998 C 3.4959912558569797 52.98420758275385 3.750683098223016 53.08875005201015 4.0159 53.0879 L 7 53.0785 L 7 56 C 7 56.5522847498308 7.447715250169207 57 8 57 L 15 57 C 15.552284749830793 57 16 56.5522847498308 16 56 L 16 53.05 L 48 52.949999999999996 L 48 56 C 48 56.5522847498308 48.4477152501692 57 49 57 L 56 57 C 56.5522847498308 57 57 56.5522847498308 57 56 L 57 52.9215 L 60.0156 52.911500000000004 C 60.56784675467124 52.90973546367597 61.01411479269045 52.46064691137152 61.0124 51.9084 L 60.9907 45 Z" stroke-linecap="round" />
</g>
</svg>
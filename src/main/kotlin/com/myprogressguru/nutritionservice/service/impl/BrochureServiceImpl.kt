package com.myprogressguru.nutritionservice.service.impl

import com.myprogressguru.cloudutils.service.EmailService
import com.myprogressguru.commonutils.service.DateService
import com.myprogressguru.nutritionservice.entity.Brochure
import com.myprogressguru.nutritionservice.enumeration.BrochureStore
import com.myprogressguru.nutritionservice.repository.BrochureRepository
import com.myprogressguru.nutritionservice.service.BrochureService
import com.myprogressguru.nutritionservice.service.model.BrochureDatesResponse
import jakarta.transaction.Transactional
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.jsoup.Jsoup
import org.jsoup.nodes.Document
import org.springframework.ai.chat.client.ChatClient
import org.springframework.ai.converter.BeanOutputConverter
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.env.Environment
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import javax.annotation.PostConstruct


@Service
class BrochureServiceImpl(
    private val dateService: DateService,
    private val brochureRepository: BrochureRepository,
    @Value("\${mpg.admin-email}") private val adminEmail: String,
    @Value("\${mpg.moderator-email}") private val moderatorEmail: String,
    private val emailService: EmailService,
    private val chatClient: ChatClient,
    private val environment: Environment
) : BrochureService {

    private val lidlRegex = """https://www.lidl.bg/l/bg/broshura/(\d{2}-\d{2})-(\d{2}-\d{2})""".toRegex()
    private val kauflandRegex = """Kaufland-(\d{2}-\d{2}-\d{4})-(\d{2}-\d{2}-\d{4})""".toRegex()
    private val billaRegex = """digital_leaflet_(\d{2}-\d{2})-(\d{2}-\d{2})-(\d{2,4})""".toRegex()
    private val fantasticoV2Regex = """Фантастико\s+брошура.*\b2\d{3}\b""".toRegex()


    @PostConstruct
    @Transactional
    @Scheduled(cron = "0 0 */3 * * *")
    @SchedulerLock(name = "run-new-brochures-check")
    override fun runChecks() {
        if (!environment.matchesProfiles("prod")) return

        val brochures = getBrochures()

        val newBrochures = brochures.filter {
            !brochureRepository.existsByStoreAndStartDateAndEndDate(
                it.store,
                it.startDate,
                it.endDate
            )
        }

        if (newBrochures.isNotEmpty()) {
            sendEmail(newBrochures)
            brochureRepository.saveAll(newBrochures)
        }
    }

    private fun sendEmail(newBrochures: List<Brochure>) {
        val formatter = DateTimeFormatter.ofPattern("dd/MM")
        val brochuresText =
            newBrochures.map { "${it.store} | ${it.startDate.format(formatter)} - ${it.endDate.format(formatter)}" }
        val subject = "New brochures available"
        val message = brochuresText.joinToString("\n-------------------\n")

        emailService.sendEmail(adminEmail, subject, message)
        emailService.sendEmail(moderatorEmail, subject, message)
    }

    private fun getBrochures(): List<Brochure> {
        return listOfNotNull(
            getBrochure(BrochureStore.LIDL, "https://www.lidl.bg/c/broshura/s10020060", lidlRegex),
            getBrochure(
                BrochureStore.KAUFLAND,
                "https://www.kaufland.bg/broshuri.html",
                kauflandRegex,
                shortDate = false
            ),
            getBrochure(
                BrochureStore.BILLA,
                "https://www.billa.bg/promocii/sedmichna-broshura",
                billaRegex,
            ),
            getBrochure(
                BrochureStore.BILLA,
                "https://www.billa.bg/billa-predstoyascha-sedmichna-broshura",
                billaRegex,
            ),
//            *getFantasticoBrochures().toTypedArray()
        )
    }

    private fun getFantasticoBrochures(): List<Brochure> {
        val doc: Document = Jsoup.connect("https://www.fantastico.bg/special-offers").get()
        val pageText = doc.outerHtml()

        val converter = BeanOutputConverter(Array<BrochureDatesResponse>::class.java)

        val matches = fantasticoV2Regex.findAll(pageText)

        val brochuresDates = chatClient.prompt()
            .system(
                """You are a date extractor from text. I want you to provide me the dates in the following format: YYYY-MM-DD
                Format of the response: ${converter.format}
            """.trimMargin()
            )
            .user(matches.map { it.value }.joinToString("\n"))
            .call()
            .entity(converter)
            ?: return emptyList()

        return brochuresDates.map { Brochure(BrochureStore.FANTASTICO, it.startDate, it.endDate) }
    }

    private fun getBrochure(store: BrochureStore, url: String, regex: Regex, shortDate: Boolean = true): Brochure? {
        val dates = findDates(url, regex)

        val (startDate, endDate) = if (shortDate) {
            formatShortDates(dates) ?: return null
        } else {
            formatLongDates(dates) ?: return null
        }

        return Brochure(store, startDate, endDate)
    }

    private fun findDates(url: String, regex: Regex): List<String> {
        val doc: Document = Jsoup.connect(url).get()
        val pageText = doc.outerHtml()

        val match = regex.find(pageText) ?: return emptyList()

        return match.groupValues.drop(1)
    }

    private fun formatShortDates(dates: List<String>): Pair<LocalDate, LocalDate>? {
        if (dates.size < 2) return null

        val formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy")

        val startDate = LocalDate.parse(
            dates[0] + "-" + dateService.getCurrentDate().year,
            formatter
        )

        val endDate = LocalDate.parse(
            dates[1] + "-" + dateService.getCurrentDate().year,
            formatter
        )

        return startDate to endDate
    }

    private fun formatLongDates(dates: List<String>): Pair<LocalDate, LocalDate>? {
        if (dates.size < 2) return null

        val formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy")

        val startDate = LocalDate.parse(
            dates[0],
            formatter
        )

        val endDate = LocalDate.parse(
            dates[1],
            formatter
        )

        return startDate to endDate
    }
}